from datetime import datetime, timedelta
from sql_query import get_row_data, get_dim_result
import pandas as pd
import warnings
from openai import OpenAI
from db_config import DATABASE_TABLES, get_gmv_table, get_activity_table, get_activity_analysis_table
import re
import itertools
import math

client = OpenAI(
    api_key = "1efe50eb-2407-4f4c-8fd3-275db7202fb8",
    base_url = "https://ark.cn-beijing.volces.com/api/v3",
)

# allowed_values = ["711","百汇","大润发","共橙","好特卖","华润","惠宜选","吉慕","佳佳","佳美","家家悦","江小囤","京东便利店","昆仑好客","懒猫超市","零食有鸣","罗森","美好超市","美团买菜","美宜佳","全家便利店","散店-便利店","散店-超市","松鼠单体加盟","万辉","文峰千家惠","沃尔玛","物美","小柴购","熊猫很忙","永辉","优购哆"]

warnings.filterwarnings('ignore')

def build_multi_select_condition(field_name, value, default_value='全部'):
    """
    构建多选条件的SQL片段
    
    Args:
        field_name: 字段名
        value: 参数值，可能是单个值或逗号分隔的多个值
        default_value: 默认值，通常是'全部'
    
    Returns:
        SQL条件字符串
    """
    if value == default_value or not value:
        return ''
    
    # 处理 coupon_threshold 的特殊情况
    if field_name == 'coupon_threshold':
        # 如果是多选，构建复杂的条件
        if ',' in str(value):
            conditions = []
            values = [v.strip() for v in str(value).split(',') if v.strip()]
            for v in values:
                if v == '其他':
                    conditions.append('coupon_threshold is null')
                elif v == '20以下':
                    conditions.append('coupon_threshold <= 20')
                elif v == '200以上':
                    conditions.append('coupon_threshold > 200')
                elif '-' in v:
                    threshold_min, threshold_max = v.split('-')
                    conditions.append(f'coupon_threshold between {threshold_min} and {threshold_max}')
            if conditions:
                return f'and ({" OR ".join(conditions)})'
            else:
                return ''
        # 单选情况
        elif value == '其他':
            return 'and coupon_threshold is null'
        elif value == '20以下':
            return 'and coupon_threshold <= 20'
        elif value == '200以上':
            return 'and coupon_threshold > 200'
        elif '-' in value:
            threshold_min, threshold_max = value.split('-')
            return f'and coupon_threshold between {threshold_min} and {threshold_max}'
    
    # 处理 coupon_discount 的特殊情况（基于折扣计算逻辑）
    elif field_name == 'coupon_discount' or field_name == 'discount':
        # 先检查是否是多选（包含逗号）
        if ',' in str(value):
            conditions = []
            values = [v.strip() for v in str(value).split(',') if v.strip()]
            for v in values:
                if v == '其他':
                    conditions.append('coupon_threshold is null')
                elif v == '1折以下':
                    conditions.append('(coupon_threshold - coupon_face_value) / NULLIF(coupon_threshold, 0) <= 0.1')
                elif v == '9折以上':
                    conditions.append('(coupon_threshold - coupon_face_value) / NULLIF(coupon_threshold, 0) >= 0.9')
                elif '-' in v and '折' in v:
                    parts = v.replace('折', '').split('-')
                    low, high = float(parts[0]) / 10, float(parts[1]) / 10
                    conditions.append(f'(coupon_threshold - coupon_face_value) / NULLIF(coupon_threshold, 0) between {low} and {high}')
            if conditions:
                return f'and ({" OR ".join(conditions)})'
            else:
                return ''
        # 单选情况
        elif value == '其他':
            return 'and coupon_threshold is null'
        elif value == '1折以下':
            return 'and (coupon_threshold - coupon_face_value) / NULLIF(coupon_threshold, 0) <= 0.1'
        elif value == '9折以上':
            return 'and (coupon_threshold - coupon_face_value) / NULLIF(coupon_threshold, 0) >= 0.9'
        elif '-' in value and '折' in value:
            parts = value.replace('折', '').split('-')
            low, high = float(parts[0]) / 10, float(parts[1]) / 10
            return f'and (coupon_threshold - coupon_face_value) / NULLIF(coupon_threshold, 0) between {low} and {high}'
    
    # 原有的通用逻辑
    if ',' in str(value):
        values = [v.strip() for v in str(value).split(',') if v.strip()]
        if not values:
            return ''
        # 构建IN条件
        quoted_values = "', '".join(values)
        return f"and {field_name} IN ('{quoted_values}')"
    else:
        # 单选情况
        return f"and {field_name} = '{value}'" if value != '全部' else ''

def get_last_weekday(input_date):
    try:
        date_obj = datetime.strptime(input_date, '%Y%m%d')
        date_7_days_ago = date_obj - timedelta(days=7)
        return date_7_days_ago.strftime('%Y%m%d')
    except ValueError:
        return "Invalid date format. Please use 'yyyy-mm-dd'."

def get_same_weekday_last_year(input_date):
    try:
        date_obj = datetime.strptime(input_date, '%Y%m%d')
        last_year_date = date_obj - timedelta(days=365)
        last_year_week_start = last_year_date - timedelta(days=last_year_date.weekday())
        weekday_offset = date_obj.weekday()
        last_year_same_weekday = last_year_week_start + timedelta(days=weekday_offset)
        return last_year_same_weekday.strftime('%Y%m%d')
    except ValueError:
        return "Invalid date format. Please use 'yyyy-mm-dd'."


def get_coupon_threshold_for_single_date_activity(tar_date, base_date, brand, sub_brand, province, city, vender, coupon_mechanism, coupon_discount, platform, upc):
    trim_tar_date = tar_date.replace('-', '')
    trim_base_date = base_date.replace('-', '')
    sql = f"""
            SELECT 
                threshold
            FROM (
                SELECT 
                    CASE 
                        WHEN coupon_threshold IS NULL THEN '其他'
                        WHEN coupon_threshold BETWEEN 0 AND 20 THEN '20以下'
                        WHEN coupon_threshold BETWEEN 21 AND 40 THEN '21-40'
                        WHEN coupon_threshold BETWEEN 41 AND 60 THEN '41-60'
                        WHEN coupon_threshold BETWEEN 61 AND 80 THEN '61-80'
                        WHEN coupon_threshold BETWEEN 81 AND 100 THEN '81-100'
                        WHEN coupon_threshold BETWEEN 101 AND 120 THEN '101-120'
                        WHEN coupon_threshold BETWEEN 121 AND 140 THEN '121-140'
                        WHEN coupon_threshold BETWEEN 141 AND 160 THEN '141-160'
                        WHEN coupon_threshold BETWEEN 161 AND 180 THEN '161-180'
                        WHEN coupon_threshold BETWEEN 181 AND 200 THEN '181-200'
                        WHEN coupon_threshold > 200 THEN '200以上'
                    END AS threshold
                FROM (
                    SELECT 
                        coupon_name, 
                        CAST(coupon_threshold AS INTEGER) AS coupon_threshold
                    FROM 
                        {get_activity_table(brand)}
                    WHERE 
                        (ds = '{trim_tar_date}' or ds = '{trim_base_date}')
                        AND brand = '{brand}'
                        {build_multi_select_condition('sub_brand', sub_brand)}
                        {build_multi_select_condition('province', province)}
                        {build_multi_select_condition('city', city)}
                        {build_multi_select_condition('vender_name', vender)}
                        {build_multi_select_condition('coupon_name', coupon_mechanism)}
                        {build_multi_select_condition('coupon_discount', coupon_discount)}
                        {build_multi_select_condition('platform', platform)}
                        {build_multi_select_condition('upc', upc)}
                ) t
            ) t1
            GROUP BY threshold
            ORDER BY 
                CASE 
                    WHEN threshold = '其他' THEN 999
                    WHEN threshold = '200以上' THEN 200
                    WHEN threshold LIKE '%以下' THEN 0
                    ELSE CAST(SPLIT_PART(threshold, '-', 1) AS INTEGER)
                END;
            """
    df = get_row_data(sql)
    return df

def get_coupon_discount_for_single_date_activity(tar_date, base_date, brand, sub_brand, province, city, vender, coupon_mechanism, coupon_threshold, platform, upc):
    trim_tar_date = tar_date.replace('-', '')
    trim_base_date = base_date.replace('-', '')
    sql = f"""
            select discount from
            (select 
            coupon_name,
            case 
            when coupon_threshold is null then '其他'
            else cast(coupon_threshold as varchar) 
            end as threshold,
            CASE WHEN discount_detail BETWEEN 0 AND 0.09999 THEN
                '1折以下'
            WHEN discount_detail BETWEEN 0.1 AND 0.19999 THEN
                '1-2折'
            WHEN discount_detail BETWEEN 0.2 AND 0.29999 THEN
                '2-3折'
            WHEN discount_detail BETWEEN 0.3 AND 0.39999 THEN
                '3-4折'
            WHEN discount_detail BETWEEN 0.4 AND 0.49999 THEN
                '4-5折'
            WHEN discount_detail BETWEEN 0.5 AND 0.59999 THEN
                '5-6折'
            WHEN discount_detail BETWEEN 0.6 AND 0.69999 THEN
                '6-7折'
            WHEN discount_detail BETWEEN 0.7 AND 0.79999 THEN
                '7-8折'
            WHEN discount_detail BETWEEN 0.8 AND 0.89999 THEN
                '8-9折'
            WHEN discount_detail BETWEEN 0.9 AND 1 THEN
                '9折以上'
            WHEN discount_detail IS NULL THEN
                '其他'
            end as discount
            from
            (select coupon_name, cast(coupon_threshold as int) as coupon_threshold,
            case
            when coupon_group = '券' then (coupon_threshold - coupon_face_value) / NULLIF(coupon_threshold, 0)
            when coupon_group like '%折扣%' then (activity_gmv - activity_expense) / NULLIF(activity_gmv, 0)
            else null end
            as discount_detail
            from {get_activity_table(brand)}
            where (ds = '{trim_tar_date}' or ds = '{trim_base_date}')
            and brand = '{brand}'
            {build_multi_select_condition('sub_brand', sub_brand)}
            {build_multi_select_condition('province', province)}
            {build_multi_select_condition('city', city)}
            {build_multi_select_condition('vender_name', vender)}
            {build_multi_select_condition('coupon_name', coupon_mechanism)}
            {build_multi_select_condition('coupon_threshold', coupon_threshold)}
            {build_multi_select_condition('platform', platform)}
            {build_multi_select_condition('upc', upc)}
            ) t) t1
            group by discount
            order by discount;
            """
    df = get_row_data(sql)
    return df

def get_coupon_threshold_for_date_range_activity(tar_start_date, tar_end_date, base_start_date, base_end_date, brand, sub_brand, province, city, vender, coupon_mechanism, coupon_discount, platform, upc):
    trim_tar_start_date = tar_start_date.replace('-', '')
    trim_tar_end_date = tar_end_date.replace('-', '')
    trim_base_start_date = base_start_date.replace('-', '')
    trim_base_end_date = base_end_date.replace('-', '')
    sql = f"""
            SELECT 
                threshold
            FROM (
                SELECT 
                    CASE 
                        WHEN coupon_threshold IS NULL THEN '其他'
                        WHEN coupon_threshold BETWEEN 0 AND 20 THEN '20以下'
                        WHEN coupon_threshold BETWEEN 21 AND 40 THEN '21-40'
                        WHEN coupon_threshold BETWEEN 41 AND 60 THEN '41-60'
                        WHEN coupon_threshold BETWEEN 61 AND 80 THEN '61-80'
                        WHEN coupon_threshold BETWEEN 81 AND 100 THEN '81-100'
                        WHEN coupon_threshold BETWEEN 101 AND 120 THEN '101-120'
                        WHEN coupon_threshold BETWEEN 121 AND 140 THEN '121-140'
                        WHEN coupon_threshold BETWEEN 141 AND 160 THEN '141-160'
                        WHEN coupon_threshold BETWEEN 161 AND 180 THEN '161-180'
                        WHEN coupon_threshold BETWEEN 181 AND 200 THEN '181-200'
                        WHEN coupon_threshold > 200 THEN '200以上'
                    END AS threshold
                FROM (
                    SELECT 
                        coupon_name, 
                        CAST(coupon_threshold AS INTEGER) AS coupon_threshold
                    FROM 
                        {get_activity_table(brand)}
                    WHERE 
                        (ds between '{trim_tar_start_date}' and '{trim_tar_end_date}' or ds between '{trim_base_start_date}' and '{trim_base_end_date}')
                        AND brand = '{brand}'
                        {build_multi_select_condition('sub_brand', sub_brand)}
                        {build_multi_select_condition('province', province)}
                        {build_multi_select_condition('city', city)}
                        {build_multi_select_condition('vender_name', vender)}
                        {build_multi_select_condition('coupon_name', coupon_mechanism)}
                        {build_multi_select_condition('coupon_discount', coupon_discount)}
                        {build_multi_select_condition('platform', platform)}
                        {build_multi_select_condition('upc', upc)}
                ) t
            ) t1
            GROUP BY threshold
            ORDER BY 
                CASE 
                    WHEN threshold = '其他' THEN 999
                    WHEN threshold = '200以上' THEN 200
                    WHEN threshold LIKE '%以下' THEN 0
                    ELSE CAST(SPLIT_PART(threshold, '-', 1) AS INTEGER)
                END;
            """
    df = get_row_data(sql)
    return df

def get_coupon_discount_for_date_range_activity(tar_start_date, tar_end_date, base_start_date, base_end_date, brand, sub_brand, province, city, vender, coupon_mechanism, coupon_threshold, platform, upc):
    trim_tar_start_date = tar_start_date.replace('-', '')
    trim_tar_end_date = tar_end_date.replace('-', '')
    trim_base_start_date = base_start_date.replace('-', '')
    trim_base_end_date = base_end_date.replace('-', '')
    sql = f"""
            select discount from
            (select 
            coupon_name,
            case 
            when coupon_threshold is null then '其他'
            else cast(coupon_threshold as varchar) 
            end as threshold,
            CASE WHEN discount_detail BETWEEN 0 AND 0.09999 THEN
                '1折以下'
            WHEN discount_detail BETWEEN 0.1 AND 0.19999 THEN
                '1-2折'
            WHEN discount_detail BETWEEN 0.2 AND 0.29999 THEN
                '2-3折'
            WHEN discount_detail BETWEEN 0.3 AND 0.39999 THEN
                '3-4折'
            WHEN discount_detail BETWEEN 0.4 AND 0.49999 THEN
                '4-5折'
            WHEN discount_detail BETWEEN 0.5 AND 0.59999 THEN
                '5-6折'
            WHEN discount_detail BETWEEN 0.6 AND 0.69999 THEN
                '6-7折'
            WHEN discount_detail BETWEEN 0.7 AND 0.79999 THEN
                '7-8折'
            WHEN discount_detail BETWEEN 0.8 AND 0.89999 THEN
                '8-9折'
            WHEN discount_detail BETWEEN 0.9 AND 1 THEN
                '9折以上'
            WHEN discount_detail IS NULL THEN
                '其他'
            end as discount
            from
            (select coupon_name, cast(coupon_threshold as int) as coupon_threshold,
            case
            when coupon_group = '券' then (coupon_threshold - coupon_face_value) / NULLIF(coupon_threshold, 0)
            when coupon_group like '%折扣%' then (activity_gmv - activity_expense) / NULLIF(activity_gmv, 0)
            else null end
            as discount_detail
            from {get_activity_table(brand)}
            where (ds between '{trim_tar_start_date}' and '{trim_tar_end_date}' or ds between '{trim_base_start_date}' and '{trim_base_end_date}')
            and brand = '{brand}'
            {build_multi_select_condition('sub_brand', sub_brand)}
            {build_multi_select_condition('province', province)}
            {build_multi_select_condition('city', city)}
            {build_multi_select_condition('vender_name', vender)}
            {build_multi_select_condition('coupon_name', coupon_mechanism)}
            {build_multi_select_condition('coupon_threshold', coupon_threshold)}
            {build_multi_select_condition('platform', platform)}
            {build_multi_select_condition('upc', upc)}
            ) t) t1
            group by discount
            order by discount;
            """
    df = get_row_data(sql)
    return df

def get_gmv_contribution_by_dim(flag, dim, brand, tar_date, base_date, sub_brand, province, city, vender, platform='全部', upc='全部'):
    sub_where = ""
    sub_where += build_multi_select_condition('sub_brand', sub_brand)
    sub_where += build_multi_select_condition('province', province)
    sub_where += build_multi_select_condition('standard_city', city)
    sub_where += build_multi_select_condition('vender_name', vender)
    sub_where += build_multi_select_condition('platform', platform)
    sub_where += build_multi_select_condition('upc', upc)
    city_condition = build_multi_select_condition('standard_city', city)

    if dim == 'sub_brand':
        field_data = "a.sub_brand"
    elif dim == 'province':
        field_data = "a.province"
    elif dim == 'standard_city':
        field_data = "a.standard_city"
    elif dim == 'vender_name':
        field_data = "a.vender_name"
    elif dim == 'product_name':
        field_data = "a.product_name"
        # 增加UPC字段
        upc_field = ", a.upc"
    elif dim == 'platform':
        field_data = "a.platform"
    else:
        field_data = "a.product_name"
        upc_field = ""
        
    if flag == 1:
        if dim == 'product_name':
            sql = f"""
            WITH gmv_data AS (
                SELECT 
                    {field_data} as {dim}{upc_field},
                    SUM(CASE WHEN ds = '{base_date}' THEN gmv ELSE 0 END) AS gmv_base,
                    SUM(CASE WHEN ds = '{tar_date}' THEN gmv ELSE 0 END) AS gmv_target
                FROM
                    {get_gmv_table(brand)} a
                WHERE 
                   
                    (ds = '{base_date}' OR ds = '{tar_date}')
                    AND {field_data} IS NOT NULL
                    and brand_filter_condition = '{brand}'
                    {sub_where}
                GROUP BY {field_data}{upc_field}
            )
            SELECT 
                {dim},
                upc,
                gmv_target,
                gmv_base,
                TO_CHAR(round(gmv_target / NULLIF(SUM(gmv_target) OVER(), 0) * 100, 2), 'FM999990.00') || '%' as target_ratio,
                TO_CHAR(round(gmv_base / NULLIF(SUM(gmv_base) OVER(), 0) * 100, 2), 'FM999990.00') || '%' as base_ratio,
                gmv_target - gmv_base as gmv_change, 
                CAST((gmv_target - gmv_base) AS DECIMAL)/NULLIF(CAST(gmv_base AS DECIMAL), 0) as gmv_change_ratio,
                case 
                when SUM(gmv_target) OVER() - SUM(gmv_base) OVER() = 0 then '0.00%'
                when SUM(gmv_target) OVER() - SUM(gmv_base) OVER() > 0 then TO_CHAR(round((gmv_target - gmv_base) / NULLIF((SUM(gmv_target) OVER() - SUM(gmv_base) OVER()), 0) * 100, 2), 'FM999990.00') || '%'
                when SUM(gmv_target) OVER() - SUM(gmv_base) OVER() < 0 then TO_CHAR(round((gmv_target - gmv_base) / NULLIF((SUM(gmv_base) OVER() - SUM(gmv_target) OVER()), 0) * 100, 2), 'FM999990.00') || '%'
                end AS contribution_to_total
            FROM
                gmv_data
            """
            print(f"商品维度SQL查询：{sql}")
        else:
            sql = f"""
            WITH gmv_data AS (
                SELECT 
                    {field_data} as {dim},
                    SUM(CASE WHEN ds = '{base_date}' THEN gmv ELSE 0 END) AS gmv_base,
                    SUM(CASE WHEN ds = '{tar_date}' THEN gmv ELSE 0 END) AS gmv_target
                FROM
                    {get_gmv_table(brand)} a
                WHERE 
                   
                    (ds = '{base_date}' OR ds = '{tar_date}')
                    AND {field_data} IS NOT NULL
                    and brand_filter_condition = '{brand}'
                    {sub_where}
                GROUP BY {field_data}
            )
            SELECT 
                {dim},
                gmv_target,
                gmv_base,
                TO_CHAR(round(gmv_target / NULLIF(SUM(gmv_target) OVER(), 0) * 100, 2), 'FM999990.00') || '%' as target_ratio,
                TO_CHAR(round(gmv_base / NULLIF(SUM(gmv_base) OVER(), 0) * 100, 2), 'FM999990.00') || '%' as base_ratio,
                gmv_target - gmv_base as gmv_change, 
                CAST((gmv_target - gmv_base) AS DECIMAL)/NULLIF(CAST(gmv_base AS DECIMAL), 0) as gmv_change_ratio,
                case 
                when SUM(gmv_target) OVER() - SUM(gmv_base) OVER() = 0 then '0.00%'
                when SUM(gmv_target) OVER() - SUM(gmv_base) OVER() > 0 then TO_CHAR(round((gmv_target - gmv_base) / NULLIF((SUM(gmv_target) OVER() - SUM(gmv_base) OVER()), 0) * 100, 2), 'FM999990.00') || '%'
                when SUM(gmv_target) OVER() - SUM(gmv_base) OVER() < 0 then TO_CHAR(round((gmv_target - gmv_base) / NULLIF((SUM(gmv_base) OVER() - SUM(gmv_target) OVER()), 0) * 100, 2), 'FM999990.00') || '%'
                end AS contribution_to_total
            FROM
                gmv_data
            """
    elif flag == 2:
        target_date_range_clause = f"(ds between '{tar_date.split('至')[0]}' and '{tar_date.split('至')[1]}')"
        base_date_range_clause = f"(ds between '{base_date.split('至')[0]}' and '{base_date.split('至')[1]}')"
        
        if dim == 'product_name':
            sql = f"""
            WITH gmv_data AS (
                SELECT 
                    {field_data} as {dim}{upc_field},
                    SUM(CASE WHEN {base_date_range_clause} THEN gmv ELSE 0 END) AS gmv_base,
                    SUM(CASE WHEN {target_date_range_clause} THEN gmv ELSE 0 END) AS gmv_target
                FROM
                    {get_gmv_table(brand)} a
                WHERE 
                   
                    ({base_date_range_clause} OR {target_date_range_clause})
                    AND {field_data} IS NOT NULL
                    and brand_filter_condition = '{brand}'
                    {sub_where}
                GROUP BY {field_data}{upc_field}
            )
            SELECT 
                {dim},
                upc,
                gmv_target,
                gmv_base,
                TO_CHAR(round(gmv_target / NULLIF(SUM(gmv_target) OVER(), 0) * 100, 2), 'FM999990.00') || '%' as target_ratio,
                TO_CHAR(round(gmv_base / NULLIF(SUM(gmv_base) OVER(), 0) * 100, 2), 'FM999990.00') || '%' as base_ratio,
                gmv_target - gmv_base as gmv_change, 
                CAST((gmv_target - gmv_base) AS DECIMAL)/NULLIF(CAST(gmv_base AS DECIMAL), 0) as gmv_change_ratio,
                case 
                when SUM(gmv_target) OVER() - SUM(gmv_base) OVER() = 0 then '0.00%'
                when SUM(gmv_target) OVER() - SUM(gmv_base) OVER() > 0 then TO_CHAR(round((gmv_target - gmv_base) / NULLIF((SUM(gmv_target) OVER() - SUM(gmv_base) OVER()), 0) * 100, 2), 'FM999990.00') || '%'
                when SUM(gmv_target) OVER() - SUM(gmv_base) OVER() < 0 then TO_CHAR(round((gmv_target - gmv_base) / NULLIF((SUM(gmv_base) OVER() - SUM(gmv_target) OVER()), 0) * 100, 2), 'FM999990.00') || '%'
                end AS contribution_to_total
            FROM
                gmv_data
            """
            print(f"商品维度SQL查询：{sql}")
        else:
            sql = f"""
            WITH gmv_data AS (
                SELECT 
                    {field_data} as {dim},
                    SUM(CASE WHEN {base_date_range_clause} THEN gmv ELSE 0 END) AS gmv_base,
                    SUM(CASE WHEN {target_date_range_clause} THEN gmv ELSE 0 END) AS gmv_target
                FROM
                    {get_gmv_table(brand)} a
                WHERE 
                   
                    ({base_date_range_clause} OR {target_date_range_clause})
                    AND {field_data} IS NOT NULL
                    and brand_filter_condition = '{brand}'
                    {sub_where}
                GROUP BY {field_data}
            )
            SELECT 
                {dim},
                gmv_target,
                gmv_base,
                TO_CHAR(round(gmv_target / NULLIF(SUM(gmv_target) OVER(), 0) * 100, 2), 'FM999990.00') || '%' as target_ratio,
                TO_CHAR(round(gmv_base / NULLIF(SUM(gmv_base) OVER(), 0) * 100, 2), 'FM999990.00') || '%' as base_ratio,
                gmv_target - gmv_base as gmv_change, 
                CAST((gmv_target - gmv_base) AS DECIMAL)/NULLIF(CAST(gmv_base AS DECIMAL), 0) as gmv_change_ratio,
                case 
                when SUM(gmv_target) OVER() - SUM(gmv_base) OVER() = 0 then '0.00%'
                when SUM(gmv_target) OVER() - SUM(gmv_base) OVER() > 0 then TO_CHAR(round((gmv_target - gmv_base) / NULLIF((SUM(gmv_target) OVER() - SUM(gmv_base) OVER()), 0) * 100, 2), 'FM999990.00') || '%'
                when SUM(gmv_target) OVER() - SUM(gmv_base) OVER() < 0 then TO_CHAR(round((gmv_target - gmv_base) / NULLIF((SUM(gmv_base) OVER() - SUM(gmv_target) OVER()), 0) * 100, 2), 'FM999990.00') || '%'
                end AS contribution_to_total
            FROM
                gmv_data
            """
    
    # 执行SQL并获取结果
    df = get_row_data(sql)
    
    # 为product_name维度检查并打印UPC列
    if dim == 'product_name':
        print(f"商品维度查询结果列: {df.columns.tolist()}")
        if 'upc' in df.columns:
            print(f"UPC列存在，前5个值: {df['upc'].head().tolist()}")
        else:
            print("UPC列不存在")
    
    return df






def get_activity_gmv_contribution_by_dim1(flag, dim, brand, tar_date, base_date, sub_brand, province, city, vender, coupon_mechanism, coupon_threshold, coupon_discount, platform='全部', upc='全部'):
    # 构建子查询条件
    sub_where = ""
    sub_where += build_multi_select_condition('sub_brand', sub_brand)
    sub_where += build_multi_select_condition('province', province)
    sub_where += build_multi_select_condition('city', city)
    sub_where += build_multi_select_condition('vender_name', vender)
    # 券机制使用多选逻辑
    coupon_mechanism_condition = build_multi_select_condition('coupon_name', coupon_mechanism)
    if coupon_mechanism_condition:
        sub_where += coupon_mechanism_condition + '\n'
    if coupon_threshold != '全部':
        if '-' in coupon_threshold:
            threshold_min, threshold_max = coupon_threshold.split('-')
            sub_where += f"and coupon_threshold between {threshold_min} and {threshold_max}\n"
        elif coupon_threshold == '20以下':
            sub_where += f"and coupon_threshold <= 20\n"
        elif coupon_threshold == '其他':
            sub_where += f"and coupon_threshold is null\n"
    if coupon_discount != '全部':
        if '-' in coupon_discount:
            parts = coupon_discount.replace('折', '').split('-')
            low, high = float(parts[0]) / 10, float(parts[1]) / 10
            sub_where += f"and (coupon_threshold - coupon_face_value) / NULLIF(coupon_threshold, 0) between {low} and {high}\n"
        elif coupon_discount == '1折以下':
            sub_where += f"and (coupon_threshold - coupon_face_value) / NULLIF(coupon_threshold, 0) <= 0.1\n"
        elif coupon_discount == '其他':
            sub_where += f"and coupon_threshold is null\n"
    
    sub_where += build_multi_select_condition('platform', platform)
    sub_where += build_multi_select_condition('upc', upc)
    
    if flag == 1:
        trim_tar_date = tar_date.replace('-', '')
        trim_base_date = base_date.replace('-', '')
        
        sql = f"""
            WITH gmv_data AS (
            SELECT
                {dim},
                SUM(CASE WHEN ds = '{trim_base_date}' THEN activity_gmv ELSE 0 END) AS gmv_base,
                SUM(CASE WHEN ds = '{trim_base_date}' THEN activity_expense ELSE 0 END) AS expense_base,
                SUM(CASE WHEN ds = '{trim_tar_date}' THEN activity_gmv ELSE 0 END) AS gmv_target,
                SUM(CASE WHEN ds = '{trim_tar_date}' THEN activity_expense ELSE 0 END) AS expense_target
            FROM (
                SELECT
                    ds,
                    province,
                    sub_brand,
                    vender_name,
                    coupon_name,
                    platform,
                    city,
                    product_name,
                    upc,
                    CASE WHEN coupon_threshold IS NULL THEN
                        '其他'
                    WHEN coupon_threshold BETWEEN 0 AND 20 THEN
                        '20以下'
                    WHEN coupon_threshold BETWEEN 21 AND 40 THEN
                        '21-40'
                    WHEN coupon_threshold BETWEEN 41 AND 60 THEN
                        '41-60'
                    WHEN coupon_threshold BETWEEN 61 AND 80 THEN
                        '61-80'
                    WHEN coupon_threshold BETWEEN 81 AND 100 THEN
                        '81-100'
                    WHEN coupon_threshold BETWEEN 101 AND 120 THEN
                        '101-120'
                    WHEN coupon_threshold BETWEEN 121 AND 140 THEN
                        '121-140'
                    WHEN coupon_threshold BETWEEN 141 AND 160 THEN
                        '141-160'
                    WHEN coupon_threshold BETWEEN 161 AND 180 THEN
                        '161-180'
                    WHEN coupon_threshold BETWEEN 181 AND 200 THEN
                        '181-200'
                    WHEN coupon_threshold > 200 THEN
                        '200以上'
                    END AS coupon_threshold,
                    CASE WHEN discount_detail BETWEEN 0 AND 0.09999 THEN
                        '1折以下'
                    WHEN discount_detail BETWEEN 0.1 AND 0.19999 THEN
                        '1-2折'
                    WHEN discount_detail BETWEEN 0.2 AND 0.29999 THEN
                        '2-3折'
                    WHEN discount_detail BETWEEN 0.3 AND 0.39999 THEN
                        '3-4折'
                    WHEN discount_detail BETWEEN 0.4 AND 0.49999 THEN
                        '4-5折'
                    WHEN discount_detail BETWEEN 0.5 AND 0.59999 THEN
                        '5-6折'
                    WHEN discount_detail BETWEEN 0.6 AND 0.69999 THEN
                        '6-7折'
                    WHEN discount_detail BETWEEN 0.7 AND 0.79999 THEN
                        '7-8折'
                    WHEN discount_detail BETWEEN 0.8 AND 0.89999 THEN
                        '8-9折'
                    WHEN discount_detail BETWEEN 0.9 AND 1 THEN
                        '9折以上'
                    WHEN discount_detail IS NULL THEN
                        '其他'
                    END AS coupon_discount,
                    activity_gmv,
                    activity_expense
                FROM (
                    SELECT
                        ds,
                        province,
                        sub_brand,
                        vender_name,
                        coupon_name,
                        platform,
                        city,
                        product_name,
                        upc,
                        CAST(coupon_threshold AS integer) AS coupon_threshold,
                        case
                        when coupon_group = '券' then (coupon_threshold - coupon_face_value) / NULLIF(coupon_threshold, 0)
                        when coupon_group like '%折扣%' then (activity_gmv - activity_expense) / NULLIF(activity_gmv, 0)
                        else null end
                        as discount_detail,
                        activity_gmv,
                        activity_expense
                    FROM
                        {get_activity_table(brand)}
                    WHERE
                        (ds = '{trim_base_date}' OR ds = '{trim_tar_date}')
                        AND brand = '{brand}')t) t1
            where 1=1
            {sub_where}
            GROUP BY
                {dim}
            )
            SELECT 
                {dim}, 
                gmv_target,
                TO_CHAR(round(gmv_target / NULLIF(SUM(gmv_target) OVER(), 0) * 100, 2), 'FM999990.00') || '%' as target_ratio,
                TO_CHAR(round(gmv_base / NULLIF(SUM(gmv_base) OVER(), 0) * 100, 2), 'FM999990.00') || '%' as base_ratio,
                expense_target,
                round(CAST(gmv_target AS DECIMAL)/NULLIF(CAST(expense_target AS DECIMAL), 0), 2) as roi_target,
                gmv_base,
                expense_base,
                round(CAST(gmv_base AS DECIMAL)/NULLIF(CAST(expense_base AS DECIMAL), 0), 2) as roi_base,
                gmv_target - gmv_base as gmv_change, 
                expense_target - expense_base as expense_change, 
                round(CAST(gmv_target AS DECIMAL)/NULLIF(CAST(expense_target AS DECIMAL), 0), 2) - round(CAST(gmv_base AS DECIMAL)/NULLIF(CAST(expense_base AS DECIMAL), 0), 2) as roi_change,
                CAST((gmv_target - gmv_base) AS DECIMAL)/NULLIF(CAST(gmv_base AS DECIMAL), 0) as gmv_change_ratio,
                CAST((expense_target - expense_base) AS DECIMAL)/NULLIF(CAST(expense_base AS DECIMAL), 0) as expense_change_ratio,
                (CAST(gmv_target AS DECIMAL)/NULLIF(CAST(expense_target AS DECIMAL), 0) - CAST(gmv_base AS DECIMAL)/NULLIF(CAST(expense_base AS DECIMAL), 0))/NULLIF(CAST(gmv_base AS DECIMAL)/NULLIF(CAST(expense_base AS DECIMAL), 0), 0) as roi_change_ratio,
                case 
                when SUM(gmv_target) OVER() - SUM(gmv_base) OVER() = 0 then '0.00%'
                when SUM(gmv_target) OVER() - SUM(gmv_base) OVER() > 0 then TO_CHAR(round((gmv_target - gmv_base) / NULLIF((SUM(gmv_target) OVER() - SUM(gmv_base) OVER()), 0) * 100, 2), 'FM999990.00') || '%'
                when SUM(gmv_target) OVER() - SUM(gmv_base) OVER() < 0 then TO_CHAR(round((gmv_target - gmv_base) / NULLIF((SUM(gmv_base) OVER() - SUM(gmv_target) OVER()), 0) * 100, 2), 'FM999990.00') || '%'
                end AS gmv_contribution_to_total,
                case 
                when SUM(expense_target) OVER() - SUM(expense_base) OVER() = 0 then '0.00%'
                when SUM(expense_target) OVER() - SUM(expense_base) OVER() > 0 then TO_CHAR(round((expense_target - expense_base) / NULLIF((SUM(expense_target) OVER() - SUM(expense_base) OVER()), 0) * 100, 2), 'FM999990.00') || '%'
                when SUM(expense_target) OVER() - SUM(expense_base) OVER() < 0 then TO_CHAR(round((expense_target - expense_base) / NULLIF((SUM(expense_base) OVER() - SUM(expense_target) OVER()), 0) * 100, 2), 'FM999990.00') || '%'
                end AS expense_contribution_to_total
            FROM
                gmv_data
        """
    elif flag == 2:
        tar_start_date, tar_end_date = tar_date.split('至')[0], tar_date.split('至')[1]
        base_start_date, base_end_date = base_date.split('至')[0], base_date.split('至')[1]
        trim_tar_start_date, trim_tar_end_date = tar_start_date.replace('-', ''), tar_end_date.replace('-', '')
        trim_base_start_date, trim_base_end_date = base_start_date.replace('-', ''), base_end_date.replace('-', '')
        sql = f"""
            WITH gmv_data AS (
            SELECT
                {dim},
                SUM(CASE WHEN ds between '{trim_base_start_date}' and '{trim_base_end_date}' THEN activity_gmv ELSE 0 END) AS gmv_base,
                SUM(CASE WHEN ds between '{trim_base_start_date}' and '{trim_base_end_date}' THEN activity_expense ELSE 0 END) AS expense_base,
                SUM(CASE WHEN ds between '{trim_tar_start_date}' and '{trim_tar_end_date}' THEN activity_gmv ELSE 0 END) AS gmv_target,
                SUM(CASE WHEN ds between '{trim_tar_start_date}' and '{trim_tar_end_date}' THEN activity_expense ELSE 0 END) AS expense_target
            FROM (
                SELECT
                    ds,
                    province,
                    sub_brand,
                    vender_name,
                    coupon_name,
                    platform,
                    city,
                    product_name,
                    upc,
                    CASE WHEN coupon_threshold IS NULL THEN
                        '其他'
                    WHEN coupon_threshold BETWEEN 0 AND 20 THEN
                        '20以下'
                    WHEN coupon_threshold BETWEEN 21 AND 40 THEN
                        '21-40'
                    WHEN coupon_threshold BETWEEN 41 AND 60 THEN
                        '41-60'
                    WHEN coupon_threshold BETWEEN 61 AND 80 THEN
                        '61-80'
                    WHEN coupon_threshold BETWEEN 81 AND 100 THEN
                        '81-100'
                    WHEN coupon_threshold BETWEEN 101 AND 120 THEN
                        '101-120'
                    WHEN coupon_threshold BETWEEN 121 AND 140 THEN
                        '121-140'
                    WHEN coupon_threshold BETWEEN 141 AND 160 THEN
                        '141-160'
                    WHEN coupon_threshold BETWEEN 161 AND 180 THEN
                        '161-180'
                    WHEN coupon_threshold BETWEEN 181 AND 200 THEN
                        '181-200'
                    WHEN coupon_threshold > 200 THEN
                        '200以上'
                    END AS coupon_threshold,
                    CASE WHEN discount_detail BETWEEN 0 AND 0.09999 THEN
                        '1折以下'
                    WHEN discount_detail BETWEEN 0.1 AND 0.19999 THEN
                        '1-2折'
                    WHEN discount_detail BETWEEN 0.2 AND 0.29999 THEN
                        '2-3折'
                    WHEN discount_detail BETWEEN 0.3 AND 0.39999 THEN
                        '3-4折'
                    WHEN discount_detail BETWEEN 0.4 AND 0.49999 THEN
                        '4-5折'
                    WHEN discount_detail BETWEEN 0.5 AND 0.59999 THEN
                        '5-6折'
                    WHEN discount_detail BETWEEN 0.6 AND 0.69999 THEN
                        '6-7折'
                    WHEN discount_detail BETWEEN 0.7 AND 0.79999 THEN
                        '7-8折'
                    WHEN discount_detail BETWEEN 0.8 AND 0.89999 THEN
                        '8-9折'
                    WHEN discount_detail BETWEEN 0.9 AND 1 THEN
                        '9折以上'
                    WHEN discount_detail IS NULL THEN
                        '其他'
                    END AS coupon_discount,
                    activity_gmv,
                    activity_expense
                FROM (
                    SELECT
                        ds,
                        province,
                        sub_brand,
                        vender_name,
                        coupon_name,
                        platform,
                        city,
                        product_name,
                        upc,
                        CAST(coupon_threshold AS integer) AS coupon_threshold,
                        case
                        when coupon_group = '券' then (coupon_threshold - coupon_face_value) / NULLIF(coupon_threshold, 0)
                        when coupon_group like '%折扣%' then (activity_gmv - activity_expense) / NULLIF(activity_gmv, 0)
                        else null end
                        as discount_detail,
                        activity_gmv,
                        activity_expense
                    FROM
                        {get_activity_table(brand)}
                    WHERE
                        (ds between '{trim_base_start_date}' and '{trim_base_end_date}' OR ds between '{trim_tar_start_date}' and '{trim_tar_end_date}')
                        AND brand = '{brand}')t) t1
            where 1=1
            {sub_where}
            GROUP BY
                {dim}
            )
            SELECT 
                {dim}, 
                gmv_target,
                TO_CHAR(round(gmv_target / NULLIF(SUM(gmv_target) OVER(), 0) * 100, 2), 'FM999990.00') || '%' as target_ratio,
                TO_CHAR(round(gmv_base / NULLIF(SUM(gmv_base) OVER(), 0) * 100, 2), 'FM999990.00') || '%' as base_ratio,
                expense_target,
                round(CAST(gmv_target AS DECIMAL)/NULLIF(CAST(expense_target AS DECIMAL), 0), 2) as roi_target,
                gmv_base,
                expense_base,
                round(CAST(gmv_base AS DECIMAL)/NULLIF(CAST(expense_base AS DECIMAL), 0), 2) as roi_base,
                gmv_target - gmv_base as gmv_change, 
                expense_target - expense_base as expense_change, 
                round(CAST(gmv_target AS DECIMAL)/NULLIF(CAST(expense_target AS DECIMAL), 0), 2) - round(CAST(gmv_base AS DECIMAL)/NULLIF(CAST(expense_base AS DECIMAL), 0), 2) as roi_change,
                CAST((gmv_target - gmv_base) AS DECIMAL)/NULLIF(CAST(gmv_base AS DECIMAL), 0) as gmv_change_ratio,
                CAST((expense_target - expense_base) AS DECIMAL)/NULLIF(CAST(expense_base AS DECIMAL), 0) as expense_change_ratio,
                (CAST(gmv_target AS DECIMAL)/NULLIF(CAST(expense_target AS DECIMAL), 0) - CAST(gmv_base AS DECIMAL)/NULLIF(CAST(expense_base AS DECIMAL), 0))/NULLIF(CAST(gmv_base AS DECIMAL)/NULLIF(CAST(expense_base AS DECIMAL), 0), 0) as roi_change_ratio,
                case 
                when SUM(gmv_target) OVER() - SUM(gmv_base) OVER() = 0 then '0.00%'
                when SUM(gmv_target) OVER() - SUM(gmv_base) OVER() > 0 then TO_CHAR(round((gmv_target - gmv_base) / NULLIF((SUM(gmv_target) OVER() - SUM(gmv_base) OVER()), 0) * 100, 2), 'FM999990.00') || '%'
                when SUM(gmv_target) OVER() - SUM(gmv_base) OVER() < 0 then TO_CHAR(round((gmv_target - gmv_base) / NULLIF((SUM(gmv_base) OVER() - SUM(gmv_target) OVER()), 0) * 100, 2), 'FM999990.00') || '%'
                end AS gmv_contribution_to_total,
                case 
                when SUM(expense_target) OVER() - SUM(expense_base) OVER() = 0 then '0.00%'
                when SUM(expense_target) OVER() - SUM(expense_base) OVER() > 0 then TO_CHAR(round((expense_target - expense_base) / NULLIF((SUM(expense_target) OVER() - SUM(expense_base) OVER()), 0) * 100, 2), 'FM999990.00') || '%'
                when SUM(expense_target) OVER() - SUM(expense_base) OVER() < 0 then TO_CHAR(round((expense_target - expense_base) / NULLIF((SUM(expense_base) OVER() - SUM(expense_target) OVER()), 0) * 100, 2), 'FM999990.00') || '%'
                end AS expense_contribution_to_total
            FROM
                gmv_data
        """
    # print(sql)
    df = get_row_data(sql)
    return df



def get_gmv_change_result_single_date(brand, tar_date, base_date, sub_brand, province, city, vender, platform='全部', upc='全部'):
    # 根据品牌选择对应的表名

    sub_where = ""
    sub_where += build_multi_select_condition('sub_brand', sub_brand)
    sub_where += build_multi_select_condition('province', province)
    sub_where += build_multi_select_condition('standard_city', city)
    sub_where += build_multi_select_condition('vender_name', vender)
    sub_where += build_multi_select_condition('platform', platform)
    sub_where += build_multi_select_condition('upc', upc)

    sql = f"""
        WITH gmv_data AS (
            SELECT 
                SUM(CASE WHEN ds = '{base_date}' THEN gmv ELSE 0 END) AS gmv_base,
                SUM(CASE WHEN ds = '{tar_date}' THEN gmv ELSE 0 END) AS gmv_target
            FROM
                {get_gmv_table(brand)}
            WHERE 
                (ds = '{base_date}' OR ds = '{tar_date}')
                and brand_filter_condition = '{brand}'
                {sub_where}
        )
        select 
        gmv_base,
        gmv_target,
        gmv_target - gmv_base as gmv_change,
        (gmv_target - gmv_base) / gmv_base as gmv_change_rate
        from gmv_data
        order by gmv_target desc
    """
    # print(sql)
    df = get_row_data(sql)
    print("df的值是：",df)
    return df

def get_gmv_change_result_multi_date(brand, tar_start_date, tar_end_date, base_start_date, base_end_date, sub_brand, province, city, vender, platform='全部', upc='全部'):

    sub_where = ""
    sub_where += build_multi_select_condition('sub_brand', sub_brand)
    sub_where += build_multi_select_condition('province', province)
    sub_where += build_multi_select_condition('standard_city', city)
    sub_where += build_multi_select_condition('vender_name', vender)
    sub_where += build_multi_select_condition('platform', platform)
    sub_where += build_multi_select_condition('upc', upc)

    # 注释掉不需要的日期转换
    # tar_start_noformat = tar_start_date.replace('-', '')
    # tar_end_noformat = tar_end_date.replace('-', '')
    # base_start_noformat = base_start_date.replace('-', '')
    # base_end_noformat = base_end_date.replace('-', '')

    # 收集所有相关的年份和月份
    # years = set()
    # months = set()
    
    # for date_str in [tar_start_date, tar_end_date, base_start_date, base_end_date]:
    #     clean_date = date_str.replace('-', '')
    #     if len(clean_date) >= 6:
    #         year = clean_date[:4]  # 前4位是年份
    #         month = clean_date[:6]  # 前6位是年月
    #         years.add(year)
    #         months.add(month)
    
    # 生成分区条件
    # years_condition = ', '.join([f"'{y}'" for y in years])
    # months_condition = ', '.join([f"'{m}'" for m in months])
    
    # partition_condition = f"ys in ({years_condition}) AND ms in ({months_condition})"

    sql = f"""
        WITH gmv_data AS (
            SELECT 
                SUM(CASE WHEN ds between '{base_start_date}' and '{base_end_date}' THEN gmv ELSE 0 END) AS gmv_base,
                SUM(CASE WHEN ds between '{tar_start_date}' and '{tar_end_date}' THEN gmv ELSE 0 END) AS gmv_target
            FROM
                {get_gmv_table(brand)}
            WHERE 
                
                ((ds between '{base_start_date}' and '{base_end_date}') OR (ds between '{tar_start_date}' and '{tar_end_date}'))
                and brand_filter_condition = '{brand}'
                {sub_where}
        )
        select 
        gmv_base,
        gmv_target,
        gmv_target - gmv_base as gmv_change,
        (gmv_target - gmv_base) / gmv_base as gmv_change_rate
        from gmv_data
        order by gmv_target desc
    """
    print(sql)
    df = get_row_data(sql)
    return df

def get_activity_gmv_change_result_single_date(brand, tar_date, base_date, sub_brand, province, city, vender, coupon_name, coupon_threshold, coupon_discount, platform='全部', upc='全部'):
    table_name = get_activity_table(brand)
    trim_tar_date = tar_date.replace('-', '')
    trim_base_date = base_date.replace('-', '')
    sub_where = ""
    sub_where += build_multi_select_condition('sub_brand', sub_brand)
    sub_where += build_multi_select_condition('province', province)
    sub_where += build_multi_select_condition('city', city)
    sub_where += build_multi_select_condition('vender_name', vender)
    sub_where += build_multi_select_condition('coupon_name', coupon_name)
    
    # 券门槛和券折扣需要特殊处理，暂时保持原逻辑
    if coupon_threshold != '全部':
        if '-' in coupon_threshold:
            threshold_min, threshold_max = coupon_threshold.split('-')
            sub_where += f"and coupon_threshold between {threshold_min} and {threshold_max}\n"
        elif coupon_threshold == '20以下':
            sub_where += f"and coupon_threshold <= 20\n"
        elif coupon_threshold == '其他':
            sub_where += f"and coupon_threshold is null\n"
    if coupon_discount != '全部':
        if '-' in coupon_discount:
            parts = coupon_discount.replace('折', '').split('-')
            low, high = float(parts[0]) / 10, float(parts[1]) / 10
            sub_where += f"and (coupon_threshold - coupon_face_value) / NULLIF(coupon_threshold, 0) between {low} and {high}\n"
        elif coupon_discount == '1折以下':
            sub_where += f"and (coupon_threshold - coupon_face_value) / NULLIF(coupon_threshold, 0) <= 0.1\n"
        elif coupon_discount == '其他':
            sub_where += f"and coupon_threshold is null\n"
    
    sub_where += build_multi_select_condition('platform', platform)
    sub_where += build_multi_select_condition('upc', upc)
    sql = f"""
        WITH gmv_data AS (
            SELECT 
                SUM(CASE WHEN ds = '{trim_base_date}' THEN activity_gmv ELSE 0 END) AS gmv_base,
                SUM(CASE WHEN ds = '{trim_base_date}' THEN activity_expense ELSE 0 END) AS expense_base,
                SUM(CASE WHEN ds = '{trim_tar_date}' THEN activity_gmv ELSE 0 END) AS gmv_target,
                SUM(CASE WHEN ds = '{trim_tar_date}' THEN activity_expense ELSE 0 END) AS expense_target
            FROM
                {table_name}
            WHERE 
                (ds = '{trim_base_date}' OR ds = '{trim_tar_date}')
                and brand = '{brand}'
                {sub_where}
        )
        select 
        gmv_base,
        expense_base,
        gmv_target,
        expense_target,
        gmv_target - gmv_base as gmv_change,
        expense_target - expense_base as expense_change,
        CAST((gmv_target - gmv_base) AS DECIMAL)/NULLIF(CAST(gmv_base AS DECIMAL), 0) as gmv_change_rate,
        CAST((expense_target - expense_base) AS DECIMAL)/NULLIF(CAST(expense_base AS DECIMAL), 0) as expense_change_rate
        from gmv_data
        order by gmv_target desc
    """
    # print(sql)
    df = get_row_data(sql)
    return df

def get_activity_gmv_change_result_multi_date(brand, tar_start_date, tar_end_date, base_start_date, base_end_date, sub_brand, province, city, vender, coupon_name, coupon_threshold, coupon_discount, platform='全部', upc='全部'):
    table_name = get_activity_table(brand)

    sub_where = ""
    sub_where += build_multi_select_condition('sub_brand', sub_brand)
    sub_where += build_multi_select_condition('province', province)
    sub_where += build_multi_select_condition('city', city)
    sub_where += build_multi_select_condition('vender_name', vender)
    sub_where += build_multi_select_condition('coupon_name', coupon_name)
    
    # 券门槛和券折扣需要特殊处理，暂时保持原逻辑
    if coupon_threshold != '全部':
        if '-' in coupon_threshold:
            threshold_min, threshold_max = coupon_threshold.split('-')
            sub_where += f"and coupon_threshold between {threshold_min} and {threshold_max}\n"
        elif coupon_threshold == '20以下':
            sub_where += f"and coupon_threshold <= 20\n"
        elif coupon_threshold == '其他':
            sub_where += f"and coupon_threshold is null\n"
    if coupon_discount != '全部':
        if '-' in coupon_discount:
            parts = coupon_discount.replace('折', '').split('-')
            low, high = float(parts[0]) / 10, float(parts[1]) / 10
            sub_where += f"and (coupon_threshold - coupon_face_value) / NULLIF(coupon_threshold, 0) between {low} and {high}\n"
        elif coupon_discount == '1折以下':
            sub_where += f"and (coupon_threshold - coupon_face_value) / NULLIF(coupon_threshold, 0) <= 0.1\n"
        elif coupon_discount == '其他':
            sub_where += f"and coupon_threshold is null\n"
    
    sub_where += build_multi_select_condition('platform', platform)
    sub_where += build_multi_select_condition('upc', upc)

    trim_tar_start_date = tar_start_date.replace('-', '')
    trim_tar_end_date = tar_end_date.replace('-', '')
    trim_base_start_date = base_start_date.replace('-', '')
    trim_base_end_date = base_end_date.replace('-', '')

    sql = f"""
        WITH gmv_data AS (
            SELECT 
                SUM(CASE WHEN ds between '{trim_base_start_date}' and '{trim_base_end_date}' THEN activity_gmv ELSE 0 END) AS gmv_base,
                SUM(CASE WHEN ds between '{trim_base_start_date}' and '{trim_base_end_date}' THEN activity_expense ELSE 0 END) AS expense_base,
                SUM(CASE WHEN ds between '{trim_tar_start_date}' and '{trim_tar_end_date}' THEN activity_gmv ELSE 0 END) AS gmv_target,
                SUM(CASE WHEN ds between '{trim_tar_start_date}' and '{trim_tar_end_date}' THEN activity_expense ELSE 0 END) AS expense_target
            FROM
                {table_name}
            WHERE 
                ((ds between '{trim_base_start_date}' and '{trim_base_end_date}') OR (ds between '{trim_tar_start_date}' and '{trim_tar_end_date}'))
                and brand = '{brand}'
                {sub_where}
        )
        select 
        gmv_base,
        expense_base,
        gmv_target,
        expense_target,
        gmv_target - gmv_base as gmv_change,
        expense_target - expense_base as expense_change,
        CAST((gmv_target - gmv_base) AS DECIMAL)/NULLIF(CAST(gmv_base AS DECIMAL), 0) as gmv_change_rate,
        CAST((expense_target - expense_base) AS DECIMAL)/NULLIF(CAST(expense_base AS DECIMAL), 0) as expense_change_rate
        from gmv_data
        order by gmv_target desc
    """
    print(sql)
    df = get_row_data(sql)
    return df



def get_dimension_name(dimension):
    """
    获取维度的中文名称
    """
    dimension_mapping = {
        'sub_brand': '子品牌',
        'province': '省份',
        'vender_name': '零售商',
        'product_name': '商品',
        'coupon_name': '券机制',
        'coupon_threshold': '券门槛',
        'coupon_discount': '优惠力度'
    }
    return dimension_mapping.get(dimension, dimension)

def get_attibutional_result(flag, brand, tar_date, base_date, sub_brand, province, city, vender, dim_list_output, platform='全部', upc='全部',analysis_dimensions_list=[]):
    results = {}
    merged_data = []
    
    # dim_list = ['sub_brand', 'province','standard_city', 'vender_name', 'product_name','platform']
    dim_list = analysis_dimensions_list
    for dim in dim_list:
        df = get_gmv_contribution_by_dim(flag, dim, brand, tar_date, base_date, sub_brand, province, city, vender, platform, upc)

        # 检查DataFrame是否为空或None
        if df is None or df.empty:
            print(f"维度 {dim} 没有数据，跳过处理")
            continue

        df['contribution_to_total'] = df['contribution_to_total'].str.rstrip('%').astype(float)
        if dim in dim_list_output:
            df['source'] = dim
            merged_data.append(df)
        df = df.sort_values(by='contribution_to_total', ascending=False)
        df['contribution_to_total'] = df['contribution_to_total'].map(lambda x: f"{x:.2f}%")
        results[dim] = df
    return results

def get_cross_dimension_attribution_result(flag, brand, tar_date, base_date, sub_brand, province, city, vender, 
                                         cross_dimension_combinations, platform='全部', upc='全部'):
    """
    获取交叉维度归因结果
    
    Args:
        flag: 时间类型标识
        brand: 品牌
        tar_date: 目标日期
        base_date: 基准日期  
        sub_brand: 子品牌
        province: 省份
        city: 城市
        vender: 零售商
        cross_dimension_combinations: 交叉维度组合列表
        platform: 平台
        upc: 商品代码
    
    Returns:
        dict: 交叉维度归因结果
    """
    result = {}
    
    for combo in cross_dimension_combinations:
        combo_name = "-".join(combo['chinese_dimensions'])
        print(f"处理交叉维度组合: {combo_name}")
        
        # 获取交叉维度数据
        cross_data = get_cross_dimension_gmv_data(
            flag, combo, brand, tar_date, base_date, sub_brand, province, city, vender, platform, upc
        )
        
        if cross_data:
            result[combo_name] = cross_data
            print(f"交叉维度 {combo_name} 生成了 {len(cross_data)} 条数据")
        else:
            print(f"交叉维度 {combo_name} 没有数据")
    
    return result

def get_cross_dimension_gmv_data(flag, combo, brand, tar_date, base_date, sub_brand, province, city, vender, platform='全部', upc='全部'):
    """
    获取交叉维度的GMV数据

    Args:
        flag: 时间类型标识
        combo: 维度组合信息
        brand: 品牌
        tar_date: 目标日期
        base_date: 基准日期
        sub_brand: 子品牌
        province: 省份
        city: 城市
        vender: 零售商
        platform: 平台
        upc: 商品代码

    Returns:
        list: 交叉维度GMV数据列表
    """
    try:
        print(f"=== 交叉维度查询开始 ===")
        print(f"交叉维度查询参数: brand={brand}, tar_date={tar_date}, base_date={base_date}")
        print(f"维度组合: {combo}")
        print(f"筛选条件: sub_brand={sub_brand}, province={province}, city={city}, vender={vender}, platform={platform}, upc={upc}")

        # 验证必要参数
        if not tar_date or not base_date:
            print(f"错误: 缺少必要的日期参数 - tar_date={tar_date}, base_date={base_date}")
            return []

        if not brand:
            print(f"错误: 缺少品牌参数")
            return []

        # 维度映射到数据库字段
        dimension_field_mapping = {
            '平台': 'platform',
            '省份': 'province',
            '城市': 'standard_city',
            '零售商': 'vender_name',
            '子品牌': 'sub_brand',
            '商品名称': 'product_name',
            'upc' : 'upc'
        }
        
        # 处理日期格式：全量GMV表使用YYYY-MM-DD格式，活动表使用YYYYMMDD格式
        # 通过品牌判断使用的表类型，然后决定日期格式
        table_name = get_gmv_table(brand)
        print(f"使用的表名: {table_name}")
        
        if 'activity' in table_name.lower():  # 活动表
            tar_date_formatted = tar_date.replace('-', '') if tar_date else ''
            base_date_formatted = base_date.replace('-', '') if base_date else ''
            print(f"活动表日期格式: tar_date_formatted={tar_date_formatted}, base_date_formatted={base_date_formatted}")
        else:  # GMV表使用原始YYYY-MM-DD格式
            # 如果传入的是YYYYMMDD格式，需要转换为YYYY-MM-DD格式
            if tar_date and len(tar_date) == 8 and tar_date.isdigit():
                tar_date_formatted = f"{tar_date[:4]}-{tar_date[4:6]}-{tar_date[6:8]}"
            else:
                tar_date_formatted = tar_date if tar_date else ''
                
            if base_date and len(base_date) == 8 and base_date.isdigit():
                base_date_formatted = f"{base_date[:4]}-{base_date[4:6]}-{base_date[6:8]}"
            else:
                base_date_formatted = base_date if base_date else ''
            print(f"GMV表日期格式: tar_date_formatted={tar_date_formatted}, base_date_formatted={base_date_formatted}")
        # 构建基础查询条件 - 不包含日期条件，日期条件单独处理
        base_conditions = [f"brand_filter_condition = '{brand}'"]
        
        # 添加固定筛选条件
        if sub_brand != '全部':
            condition = build_multi_select_condition('sub_brand', sub_brand)
            if condition:
                clean_condition = condition[4:] if condition.startswith('and ') else condition
                base_conditions.append(clean_condition)
        if province != '全部':
            condition = build_multi_select_condition('province', province)
            if condition:
                clean_condition = condition[4:] if condition.startswith('and ') else condition
                base_conditions.append(clean_condition)
        if city != '全部':
            condition = build_multi_select_condition('standard_city', city)
            if condition:
                clean_condition = condition[4:] if condition.startswith('and ') else condition
                base_conditions.append(clean_condition)
        if vender != '全部':
            condition = build_multi_select_condition('vender_name', vender)
            if condition:
                clean_condition = condition[4:] if condition.startswith('and ') else condition
                base_conditions.append(clean_condition)
        if platform != '全部':
            condition = build_multi_select_condition('platform', platform)
            if condition:
                clean_condition = condition[4:] if condition.startswith('and ') else condition
                base_conditions.append(clean_condition)
        if upc != '全部':
            condition = build_multi_select_condition('upc', upc)
            if condition:
                clean_condition = condition[4:] if condition.startswith('and ') else condition
                base_conditions.append(clean_condition)
        
        # 使用GROUP BY一次性获取所有交叉维度数据，避免循环查询
        dimension_names = combo['chinese_dimensions']
        db_fields = [dimension_field_mapping.get(dim) for dim in dimension_names if dimension_field_mapping.get(dim)]

        # 检查是否包含商品维度，如果包含则需要添加UPC字段
        has_product_dimension = any(dim in ['商品名称', '商品'] for dim in dimension_names)
        select_fields = db_fields.copy()
        group_by_fields = db_fields.copy()

        if has_product_dimension and 'upc' not in select_fields:
            select_fields.append('upc')
            group_by_fields.append('upc')
            print(f"检测到商品维度，添加UPC字段到查询中")

        print(f"中文维度名称: {dimension_names}")
        print(f"映射后的数据库字段: {db_fields}")
        print(f"查询字段: {select_fields}")
        print(f"分组字段: {group_by_fields}")

        if not db_fields:
            print(f"错误: 没有找到有效的数据库字段映射")
            print(f"可用的维度映射: {list(dimension_field_mapping.keys())}")
            return []
        
        # 构建完整的WHERE条件
        target_conditions = [f"ds = '{tar_date_formatted}'"] + base_conditions  # 添加目标日期条件
        base_conditions_list = [f"ds = '{base_date_formatted}'"] + base_conditions  # 添加基准日期条件

        # 添加维度字段非空条件
        dimension_conditions = [f"{field} IS NOT NULL AND {field} != '' AND {field} != '全部'" for field in select_fields]

        print(f"目标日期条件: {target_conditions}")
        print(f"基准日期条件: {base_conditions_list}")
        print(f"维度字段条件: {dimension_conditions}")

        # 根据flag构建不同的查询SQL
        if flag == 1:  # 单日期查询
            # 目标日期GMV查询
            target_sql = f"""
                SELECT SUM(gmv) as target_gmv, {', '.join(select_fields)}
                FROM {get_gmv_table(brand)}
                WHERE {' AND '.join(target_conditions + dimension_conditions)}
                GROUP BY {', '.join(group_by_fields)}
                ORDER BY SUM(gmv) DESC
            """

            # 基准日期GMV查询
            base_sql = f"""
                SELECT SUM(gmv) as base_gmv, {', '.join(select_fields)}
                FROM {get_gmv_table(brand)}
                WHERE {' AND '.join(base_conditions_list + dimension_conditions)}
                GROUP BY {', '.join(group_by_fields)}
            """

            # 总GMV查询 - 只包含基础筛选条件，不包含维度条件
            total_target_sql = f"""
                SELECT SUM(gmv) as total_gmv
                FROM {get_gmv_table(brand)}
                WHERE {' AND '.join(target_conditions)}
            """
            
        else:  # 日期范围查询
            # 处理日期范围格式
            if 'activity' in table_name.lower():  # 活动表使用YYYYMMDD
                tar_start = tar_date.split('至')[0].replace('-', '') if '至' in tar_date else tar_date_formatted
                tar_end = tar_date.split('至')[1].replace('-', '') if '至' in tar_date else tar_date_formatted
                base_start = base_date.split('至')[0].replace('-', '') if '至' in base_date else base_date_formatted
                base_end = base_date.split('至')[1].replace('-', '') if '至' in base_date else base_date_formatted
            else:  # GMV表使用YYYY-MM-DD
                tar_start = tar_date.split('至')[0] if '至' in tar_date else tar_date_formatted
                tar_end = tar_date.split('至')[1] if '至' in tar_date else tar_date_formatted
                base_start = base_date.split('至')[0] if '至' in base_date else base_date_formatted
                base_end = base_date.split('至')[1] if '至' in base_date else base_date_formatted

            # 构建日期范围条件
            target_range_conditions = [f"ds BETWEEN '{tar_start}' AND '{tar_end}'"] + base_conditions
            base_range_conditions = [f"ds BETWEEN '{base_start}' AND '{base_end}'"] + base_conditions

            # 目标日期范围GMV查询
            target_sql = f"""
                SELECT SUM(gmv) as target_gmv, {', '.join(select_fields)}
                FROM {get_gmv_table(brand)}
                WHERE {' AND '.join(target_range_conditions + dimension_conditions)}
                GROUP BY {', '.join(group_by_fields)}
                ORDER BY SUM(gmv) DESC
            """

            # 基准日期范围GMV查询
            base_sql = f"""
                SELECT SUM(gmv) as base_gmv, {', '.join(select_fields)}
                FROM {get_gmv_table(brand)}
                WHERE {' AND '.join(base_range_conditions + dimension_conditions)}
                GROUP BY {', '.join(group_by_fields)}
            """
            
            # 总GMV查询 - 只包含基础筛选条件，不包含维度条件
            total_target_sql = f"""
                SELECT SUM(gmv) as total_gmv
                FROM {get_gmv_table(brand)}
                WHERE {' AND '.join(target_range_conditions)}
            """
        try:
            # 打印SQL查询语句用于调试
            print(f"=== 执行目标日期查询 ===")
            print(f"目标日期SQL: {target_sql}")
            print(f"=== 执行基准日期查询 ===")
            print(f"基准日期SQL: {base_sql}")
            print(f"=== 执行总GMV查询 ===")
            print(f"总GMV SQL: {total_target_sql}")

            # 执行查询
            target_result = get_row_data(target_sql)
            base_result = get_row_data(base_sql)
            total_result = get_row_data(total_target_sql)

            print(f"目标日期查询结果: {target_result.shape if target_result is not None else 'None'}")
            print(f"基准日期查询结果: {base_result.shape if base_result is not None else 'None'}")
            print(f"总GMV查询结果: {total_result.shape if total_result is not None else 'None'}")

            if target_result is None or target_result.empty:
                print(f"错误: 目标日期没有数据")
                return []
            
            if total_result is None or total_result.empty:
                total_gmv = 1
            else:
                total_gmv = float(total_result.iloc[0]['total_gmv']) if total_result.iloc[0]['total_gmv'] else 1
            
            print(f"找到 {len(target_result)} 个交叉维度组合")

            # 限制处理的组合数量以提高性能
            max_combinations = 1000  # 限制最多处理1000个组合
            if len(target_result) > max_combinations:
                print(f"数据量过大，限制处理前 {max_combinations} 个组合")
                target_result = target_result.head(max_combinations)

            # 将基准数据转换为字典便于查找
            base_dict = {}
            if base_result is not None and not base_result.empty:
                for _, row in base_result.iterrows():
                    key = tuple(row[field] for field in group_by_fields)
                    base_dict[key] = float(row['base_gmv']) if row['base_gmv'] else 0

            # 预先计算总基准GMV，避免在循环中重复查询
            if flag == 1:  # 单日期模式
                total_base_sql = f"""
                    SELECT SUM(gmv) as total_base_gmv
                    FROM {get_gmv_table(brand)}
                    WHERE ds = '{base_date_formatted}' AND brand_filter_condition = '{brand}'
                """
            else:  # 日期范围模式
                total_base_sql = f"""
                    SELECT SUM(gmv) as total_base_gmv
                    FROM {get_gmv_table(brand)}
                    WHERE ds BETWEEN '{base_start}' AND '{base_end}' AND brand_filter_condition = '{brand}'
                """
            total_base_result = get_row_data(total_base_sql)
            total_base_gmv = float(total_base_result.iloc[0]['total_base_gmv']) if (total_base_result is not None and not total_base_result.empty and total_base_result.iloc[0]['total_base_gmv']) else 1

            # 生成交叉维度结果
            cross_results = []
            total_combinations = len(target_result)
            processed_count = 0

            print(f"开始处理 {total_combinations} 个交叉维度组合...")

            for _, row in target_result.iterrows():
                processed_count += 1
                if processed_count % 1000 == 0:
                    print(f"已处理 {processed_count}/{total_combinations} 个组合 ({processed_count/total_combinations*100:.1f}%)")
                # 构建维度组合
                combination = []
                cross_dim_values = {}
                upc_value = None

                # 处理主要维度字段
                for i, field in enumerate(db_fields):
                    dim_value = row[field]
                    combination.append(dim_value)
                    cross_dim_values[dimension_names[i]] = dim_value

                # 如果包含商品维度，处理UPC字段
                if has_product_dimension and 'upc' in row:
                    upc_value = row['upc']
                    # 将UPC添加到交叉维度名称中（对于商品维度）
                    for i, dim_name in enumerate(dimension_names):
                        if dim_name in ['商品名称', '商品'] and i < len(combination):
                            combination[i] = f"{combination[i]}(UPC:{upc_value})"

                # 获取GMV数据 - 使用所有分组字段作为key
                target_gmv = float(row['target_gmv']) if row['target_gmv'] else 0
                key = tuple(row[field] for field in group_by_fields)
                base_gmv = base_dict.get(key, 0)

                if target_gmv > 0 or base_gmv > 0:
                    # 计算各项指标
                    gmv_change = target_gmv - base_gmv
                    gmv_change_rate = (gmv_change / base_gmv * 100) if base_gmv > 0 else 0
                    target_ratio = (target_gmv / total_gmv * 100) if total_gmv > 0 else 0
                    base_ratio = (base_gmv / total_base_gmv * 100) if total_base_gmv > 0 else 0
                    gmv_contribution = (gmv_change / total_gmv * 100) if total_gmv > 0 else 0

                    # 创建交叉维度记录
                    cross_record = {}

                    # 添加交叉维度名称
                    cross_name = '-'.join(combination)
                    cross_record['交叉维度名称'] = cross_name

                    # 如果有UPC信息，单独添加UPC字段
                    if upc_value:
                        cross_record['UPC'] = upc_value
                    
                    # 只添加必要的GMV相关指标
                    cross_record.update({
                        '当期GMV': round(target_gmv, 2),
                        '当期GMV占比': f"{target_ratio:.2f}%",
                        '对比期GMV': round(base_gmv, 2),
                        '对比期GMV占比': f"{base_ratio:.2f}%",
                        'GMV变化值': round(gmv_change, 2),
                        'GMV变化率': f"{gmv_change_rate:+.2f}%",
                        'GMV贡献度': f"{gmv_contribution:+.2f}%"
                    })
                    
                    cross_results.append(cross_record)
                    
        except Exception as e:
            print(f"交叉维度查询失败: {e}")
            import traceback
            traceback.print_exc()
            return []
        
        # 按当期GMV排序
        cross_results.sort(key=lambda x: x['当期GMV'], reverse=True)
        
        print(f"交叉维度查询完成，共生成 {len(cross_results)} 条记录")
        return cross_results
        
    except Exception as e:
        print(f"获取交叉维度GMV数据失败: {e}")
        import traceback
        traceback.print_exc()
        return []

def get_activity_attibutional_result(flag, brand, tar_date, base_date, sub_brand, province,city, vender, coupon_mechanism, coupon_threshold, coupon_discount, dim_list_output, platform='全部', upc='全部',analysis_dimensions_list=[]):
    results_for_detail = {}
    results_for_attribution = {}
    merged_data = []
    #dim_list = ['sub_brand', 'province', 'vender_name', 'coupon_name', 'coupon_threshold', 'coupon_discount','platform','city']
    dim_list = analysis_dimensions_list
    for dim in dim_list:
        df = get_activity_gmv_contribution_by_dim1(flag, dim, brand, tar_date, base_date, sub_brand, province, city, vender, coupon_mechanism, coupon_threshold, coupon_discount, platform, upc)

        # 检查DataFrame是否为空或None
        if df is None or df.empty:
            print(f"维度 {dim} 没有数据，跳过处理")
            continue

        df['roi_base'] = df['roi_base'].fillna('-')
        df['roi_target'] = df['roi_target'].fillna('-')
        df['roi_change'] = df['roi_change'].fillna('-')
        df['roi_change_ratio'] = df['roi_change_ratio'].fillna('-')
        df['gmv_contribution_to_total'] = df['gmv_contribution_to_total'].str.rstrip('%').astype(float)
        df['expense_contribution_to_total'] = df['expense_contribution_to_total'].str.rstrip('%').astype(float)
        if dim in dim_list_output:
            if dim != 'coupon_name':
                df['source'] = dim
                merged_data.append(df)
        df = df.sort_values(by='gmv_contribution_to_total', ascending=False)
        df['gmv_contribution_to_total'] = df['gmv_contribution_to_total'].map(lambda x: f"{x:.2f}%")
        df['expense_contribution_to_total'] = df['expense_contribution_to_total'].map(lambda x: f"{x:.2f}%")
        results_for_detail[dim] = df
        if dim != 'coupon_name':
            results_for_attribution[dim] = df

    return results_for_detail


ATTRIBUTION_ANALYSIS_PROMPT_GMV = """你是一个专业的数据分析师，帮我严格按照分析框架详细分析数据。

**分析框架**:
**1. 整体分析**
   - **总体GMV变化趋势**：简要描述整体增长或下降情况
   - **关键驱动因素**:分析导致GMV变化的主要原因

**2. 维度分析**
   - 按平台、省份、城市、零售商、子品牌、商品顺序，分别输出头部贡献分析、尾部贡献分析、异常波动分析
   - 注意写结论的时候必须按照平台、省份、城市、零售商、子品牌、商品这个顺序不能变动，不能缺少维度

   **头部贡献分析**
   - 识别并排序贡献度最大的（正数大于负数，不是按绝对值）,正向TOP 3-5个维度实体
   - 按贡献度从大到小排列,贡献度数据必须带正号和负号,正号不可以省略
   - 数据必须标识是贡献度还是变化率还是GMV,每一个数据都需要标注
   - **特别要求：在分析商品维度时，如果数据中包含UPC信息，必须在提及商品名称时同时标注其对应的UPC代码，格式为"商品名称(UPC: XXXXXX)"**
   - 分析头部实体的增长特征和驱动因素
   - 评估头部实体对总体GMV增长的贡献程度

   **尾部贡献分析**
   - 识别并排序贡献度最小的（正数大于负数,不是按绝对值排序）,TOP 2-5个维度实体
   - 注意最小贡献度,没有限制必须是负数,可以是正数
   - 按贡献度从大到小排列,贡献度数据必须带正号和负号,正号不可以省略
   - 数据必须标识是贡献度还是变化率还是GMV,每一个数据都需要标注
   - **特别要求：在分析商品维度时，如果数据中包含UPC信息，必须在提及商品名称时同时标注其对应的UPC代码，格式为"商品名称(UPC: XXXXXX)"**
   - 分析尾部实体快速增长的潜在原因
   - 评估尾部实体的增长潜力和可持续性

   **异常波动分析**
   - 按变化率的绝对值由大到小排序,大的为异常波动,多大为异常由AI判断,输出的变化率为原值(非绝对值),变化率数据必须带正号和负号,正号不可以省略,数据必须标识是贡献度还是变化率还是GMV。
   - **特别要求：在分析商品维度时，如果数据中包含UPC信息，必须在提及商品名称时同时标注其对应的UPC代码，格式为"商品名称(UPC: XXXXXX)"**
   - 分析异常波动的可能原因
   - 判断异常波动是偶发事件还是趋势性变化

**3. 核心结论**
   - 总结关键洞察
   - 提炼最重要的发现
   - **在总结涉及商品的关键洞察时，如果数据中包含UPC信息，必须标注UPC代码**

**4. 建议行动**
   - 针对分析结果提出具体可执行的建议
   - **在针对商品提出建议时，如果数据中包含UPC信息，必须标注UPC代码以便精准执行**

**分析要求**:
- 数据保持原始状态，不需要额外格式化
- 不需要输出任何图表
- GMV单位为元
- 所有金额单位都必须为元
- 重点关注变化率和贡献度
- 提供具体的数据支撑和洞察性结论
- 使用**加粗**突出关键信息
- 贡献度和变化率必须带正负号
- 所有数据必须标识是贡献度还是变化率还是GMV
- **商品分析特别要求：当数据中包含UPC信息时，每次提及商品都必须同时标注UPC代码，确保洞察的精准性和可执行性**
"""

ATTRIBUTION_ANALYSIS_PROMPT_ACTIVITY = """你是一个专业的数据分析师，帮我详细分析数据。

**分析框架**：
**1. 整体分析**
   - **总体活动GMV变化趋势**：简要描述整体增长或下降情况
   - **关键驱动因素**：分析导致活动GMV变化的主要原因

**2. 维度分析**
   - 按平台、省份、城市、零售商、子品牌、商品、券机制、券门槛、优惠力度顺序，分别输出头部贡献分析、尾部贡献分析、异常波动分析
   - 注意写结论的时候必须按照平台、省份、城市、零售商、子品牌、商品、券机制、券门槛、优惠力度这个顺序不能变动，不能缺少维度

   **头部贡献分析**
   - 识别并排序贡献度最大的（正数大于负数，不是按绝对值）,正向TOP 3-5个维度实体
   - 按贡献度从大到小排列,贡献度数据必须带正号和负号,正号不可以省略
   - 数据必须标识是贡献度还是变化率还是GMV,每一个数据都需要标注
   - **特别要求：在分析商品维度时，如果数据中包含UPC信息，必须在提及商品名称时同时标注其对应的UPC代码，格式为"商品名称(UPC: XXXXXX)"**
   - 分析头部实体的增长特征和驱动因素
   - 评估头部实体对总体GMV增长的贡献程度

   **尾部贡献分析**
   - 识别并排序贡献度最小的（正数大于负数,不是按绝对值排序）,TOP 2-5个维度实体
   - 注意最小贡献度,没有限制必须是负数,可以是正数
   - 按贡献度从大到小排列,贡献度数据必须带正号和负号,正号不可以省略
   - 数据必须标识是贡献度还是变化率还是GMV,每一个数据都需要标注
   - **特别要求：在分析商品维度时，如果数据中包含UPC信息，必须在提及商品名称时同时标注其对应的UPC代码，格式为"商品名称(UPC: XXXXXX)"**
   - 分析尾部实体快速增长的潜在原因
   - 评估尾部实体的增长潜力和可持续性

   **异常波动分析**
   - 按变化率的绝对值由大到小排序,大的为异常波动,多大为异常由AI判断,输出的变化率为原值(非绝对值),变化率数据必须带正号和负号,正号不可以省略,数据必须标识是贡献度还是变化率还是GMV。
   - **特别要求：在分析商品维度时，如果数据中包含UPC信息，必须在提及商品名称时同时标注其对应的UPC代码，格式为"商品名称(UPC: XXXXXX)"**
   - 分析异常波动的可能原因
   - 判断异常波动是偶发事件还是趋势性变化

**3. 核心结论**
   - 总结关键洞察
   - 提炼最重要的发现
   - **在总结涉及商品的关键洞察时，如果数据中包含UPC信息，必须标注UPC代码**

**4. 建议行动**
   - 针对分析结果提出具体可执行的建议
   - **在针对商品提出建议时，如果数据中包含UPC信息，必须标注UPC代码以便精准执行**

**分析要求**:
- 数据保持原始状态，不需要额外格式化
- 不需要输出任何图表
- GMV单位为元
- 所有金额单位都必须为元
- 重点关注变化率和贡献度
- 提供具体的数据支撑和洞察性结论
- 使用**加粗**突出关键信息
- 贡献度和变化率必须带正负号
- 所有数据必须标识是贡献度还是变化率还是GMV
- **商品分析特别要求：当数据中包含UPC信息时，每次提及商品都必须同时标注UPC代码，确保洞察的精准性和可执行性**
"""

def get_attributional_result_text(result):
    """传统维度归因分析"""
    if "券机制" in result:
        ATTRIBUTION_ANALYSIS_PROMPT = ATTRIBUTION_ANALYSIS_PROMPT_ACTIVITY
    else:
        ATTRIBUTION_ANALYSIS_PROMPT = ATTRIBUTION_ANALYSIS_PROMPT_GMV
    try:
        prompt = result
        print(f"准备调用AI归因分析，数据类型: {type(prompt)}")
        print(f"数据内容预览: {str(prompt)[:500]}...")

        # 检查数据长度
        prompt_str = str(prompt)
        print(f"数据总长度: {len(prompt_str)} 字符")

        # 如果数据过长，进行截断
        if len(prompt_str) > 150000:  # 限制在50k字符以内
            print("数据过长，进行截断处理")
            # 保留前面的关键信息，截断后面的详细数据
            prompt_str = prompt_str[:150000] + "\n\n[数据已截断，仅显示部分内容用于分析]"

        response = client.chat.completions.create(
            model="deepseek-r1-250120",
            messages=[
                {"role": "system", "content": ATTRIBUTION_ANALYSIS_PROMPT},
                {"role": "user", "content": prompt_str}
            ],
            temperature=0.7,
            presence_penalty=0.1,
            frequency_penalty=0.1
        )

        # 提取归因结论
        answer = response.choices[0].message.content.strip()

        # 在开头添加固定的第一句话
        answer = f"基于所选数据范围和分析框架，分析结论如下：\n\n{answer}"

        # 修复Markdown标题和移除代码块标记
        answer = clean_markdown_format(answer)
        print("AI归因分析调用成功")
    except Exception as e:
        print(f"AI归因分析调用失败，错误详情: {str(e)}")
        import traceback
        traceback.print_exc()
        if 'length' in str(e) or 'token' in str(e).lower():
            answer = "当前筛选条件下数据量太大，请改变筛选条件、减少数据量后重试"
        elif 'api' in str(e).lower() or 'network' in str(e).lower() or 'connection' in str(e).lower():
            answer = "AI服务暂时不可用，请稍后重试"
        else:
            answer = f"归因分析过程中出现错误：{str(e)[:100]}..."
    return answer

def clean_markdown_format(text):
    """清理Markdown格式的通用函数"""
    # 处理四级标题（#### 开头）
    text = re.sub(r'^#### (\d+\.?\s*.*?)$', r'### \1', text, flags=re.MULTILINE)

    # 处理三级标题（### 开头）
    text = re.sub(r'^### (.*?)$', r'## \1', text, flags=re.MULTILINE)

    # 处理二级标题（## 开头）
    text = re.sub(r'^## (.*?)$', r'# \1', text, flags=re.MULTILINE)

    # 移除代码块标记
    text = re.sub(r'```(.*?)```', r'\1', text, flags=re.DOTALL)
    text = re.sub(r'```\w*\n', '', text)
    text = re.sub(r'\n```', '', text)

    return text.strip()

# 供给向指标归因分析提示词
SUPPLY_SIDE_ATTRIBUTION_PROMPT = """你是一个专业的供给侧指标分析师，帮我详细分析供给向指标数据。

**分析框架**：
**1. 整体分析**
   - **总体GMV变化趋势**：简要描述整体增长或下降情况
   - **关键驱动因素**：分析导致GMV变化的主要供给侧因素

**2. 供给向指标拆解分析**
   按照GMV = 铺货门店数 × 店均在售SKU数 × SKU平均动销率 × 动销SKU平均GMV公式进行分析：

   **铺货门店数分析**
   - 当期值、对比期值、变化值、变化率、贡献度
   - 分析门店数变化的原因和影响

   **店均在售SKU数分析**
   - 当期值、对比期值、变化值、变化率、贡献度
   - 分析SKU宽度变化的原因和影响

   **SKU平均动销率分析**
   - 当期值、对比期值、变化值、变化率、贡献度
   - 分析动销效率变化的原因和影响

   **动销SKU平均GMV分析**
   - 当期值、对比期值、变化值、变化率、贡献度
   - 分析单SKU产出变化的原因和影响

**3. 综合洞察**
   - 识别最关键的供给侧驱动因素
   - 提供具体的优化建议
   - 评估各指标的改善潜力

**输出要求**：
- 所有数据必须标识单位和类型（贡献度、变化率、绝对值等）
- 贡献度和变化率必须带正负号，正号不可省略
- 使用**加粗**突出关键信息
- 提供具体的数据支撑和洞察性结论
- 重点关注供给侧指标的业务含义和优化方向
"""

# 混合指标归因分析提示词
MIXED_ATTRIBUTION_PROMPT = """你是一个专业的数据分析师，帮我详细分析包含供给向和促销项指标的混合数据。

**分析框架**：
**1. 整体分析**
   - **总体GMV变化趋势**：简要描述整体增长或下降情况
   - **供给侧vs营销侧驱动力对比**：分析两侧因素的相对重要性

**2. 供给向指标分析**
   按照GMV = 铺货门店数 × 店均在售SKU数 × SKU平均动销率 × 动销SKU平均GMV公式分析各指标贡献度

**3. 促销项指标分析**
   按照GMV = 活动机制核销金额 × 活动机制ROI + 自然GMV公式分析各指标贡献度（自然GMV = 总GMV - 去重活动GMV）

**4. 维度分析**
   - 按平台、省份、城市、零售商、子品牌、商品顺序进行维度分析
   - 每个维度输出头部贡献分析、尾部贡献分析、异常波动分析

**5. 综合洞察**
   - 供给侧和营销侧的协同效应分析
   - 识别最关键的复合驱动因素
   - 提供供给侧和营销侧的协调优化建议

**输出要求**：
- 所有数据必须标识单位和类型
- 贡献度和变化率必须带正负号
- 使用**加粗**突出关键信息
- 重点分析供给侧和营销侧的相互作用
- 输出不要有表格样式
"""

def get_supply_side_attribution_text(result):
    """供给向指标归因分析"""
    try:
        # 构建供给向指标分析的输入数据
        prompt = format_supply_side_data_for_ai(result)

        response = client.chat.completions.create(
            model="deepseek-r1-250120",
            messages=[
                {"role": "system", "content": SUPPLY_SIDE_ATTRIBUTION_PROMPT},
                {"role": "user", "content": prompt}
            ],
            temperature=0.7,
            presence_penalty=0.1,
            frequency_penalty=0.1
        )

        # 提取归因结论
        answer = response.choices[0].message.content.strip()

        # 在开头添加固定的第一句话
        answer = f"基于供给向指标分析框架，分析结论如下：\n\n{answer}"

        # 修复Markdown标题和移除代码块标记
        answer = clean_markdown_format(answer)
    except Exception as e:
        if 'length' in str(e):
            answer = "当前筛选条件下数据量太大，请改变筛选条件、减少数据量后重试"
        else:
            answer = "供给向指标归因分析出错，请稍后重试"
    return answer

def get_mixed_attribution_text(result):
    """混合指标归因分析"""
    try:
        # 构建混合指标分析的输入数据
        prompt = format_mixed_data_for_ai(result)

        response = client.chat.completions.create(
            model="deepseek-r1-250120",
            messages=[
                {"role": "system", "content": MIXED_ATTRIBUTION_PROMPT},
                {"role": "user", "content": prompt}
            ],
            temperature=0.7,
            presence_penalty=0.1,
            frequency_penalty=0.1
        )

        # 提取归因结论
        answer = response.choices[0].message.content.strip()

        # 在开头添加固定的第一句话
        answer = f"基于混合指标分析框架，分析结论如下：\n\n{answer}"

        # 修复Markdown标题和移除代码块标记
        answer = clean_markdown_format(answer)
    except Exception as e:
        if 'length' in str(e):
            answer = "当前筛选条件下数据量太大，请改变筛选条件、减少数据量后重试"
        else:
            answer = "混合指标归因分析出错，请稍后重试"
    return answer

def format_supply_side_data_for_ai(result):
    """格式化供给向指标数据用于AI分析"""
    formatted_data = []

    # 处理供给向指标数据
    if "supply_side_attribution" in result:
        supply_data = result["supply_side_attribution"]
        if "供给向指标归因" in supply_data:
            indicators = supply_data["供给向指标归因"]

            formatted_data.append("=== 供给向指标拆解数据 ===")
            formatted_data.append("GMV = 铺货门店数 × 店均在售SKU数 × SKU平均动销率 × 动销SKU平均GMV")
            formatted_data.append("")

            for indicator_name, indicator_data in indicators.items():
                formatted_data.append(f"【{indicator_name}】")
                formatted_data.append(f"当期值: {indicator_data.get('当期值', '0')}")
                formatted_data.append(f"对比期值: {indicator_data.get('对比期值', '0')}")
                formatted_data.append(f"变化值: {indicator_data.get('变化值', '0')}")
                formatted_data.append(f"变化率: {indicator_data.get('变化率', '0.00%')}")
                formatted_data.append(f"贡献度: {indicator_data.get('贡献度', '0.00%')}")
                formatted_data.append("")

    # 处理维度数据（如果有）
    dimension_order = ['平台', '省份', '城市', '零售商', '子品牌', '商品']
    for dim in dimension_order:
        if dim in result:
            formatted_data.append(f"=== {dim}维度数据 ===")
            dim_data = result[dim]
            if hasattr(dim_data, 'head'):  # 如果是DataFrame
                top_5 = dim_data.head(5)
                for _, row in top_5.iterrows():
                    formatted_data.append(f"{row.iloc[0]}: 当期GMV={row.get('gmv_target', 0)}, 变化率={row.get('gmv_change_ratio', 0)}, 贡献度={row.get('contribution_to_total', '0%')}")
            formatted_data.append("")

    return "\n".join(formatted_data)

def format_mixed_data_for_ai(result):
    """格式化混合指标数据用于AI分析"""
    formatted_data = []

    # 处理供给向指标数据
    if "supply_side_attribution" in result:
        formatted_data.append("=== 供给向指标数据 ===")
        formatted_data.extend(format_supply_side_data_for_ai(result).split('\n'))
        formatted_data.append("")

    # 处理促销项指标数据
    if "marketing_side_attribution" in result:
        formatted_data.append("=== 促销项指标数据 ===")
        marketing_data = result["marketing_side_attribution"]
        if "促销项指标归因" in marketing_data:
            indicators = marketing_data["促销项指标归因"]
            formatted_data.append("GMV = 活动机制核销金额 × 活动机制ROI + 自然GMV（自然GMV = 总GMV - 去重活动GMV）")
            formatted_data.append("")

            for indicator_name, indicator_data in indicators.items():
                formatted_data.append(f"【{indicator_name}】")
                formatted_data.append(f"当期值: {indicator_data.get('当期值', '0')}")
                formatted_data.append(f"对比期值: {indicator_data.get('对比期值', '0')}")
                formatted_data.append(f"变化值: {indicator_data.get('变化值', '0')}")
                formatted_data.append(f"变化率: {indicator_data.get('变化率', '0.00%')}")
                formatted_data.append(f"贡献度: {indicator_data.get('贡献度', '0.00%')}")
                formatted_data.append("")

    # 处理维度数据
    dimension_order = ['平台', '省份', '城市', '零售商', '子品牌', '商品']
    for dim in dimension_order:
        if dim in result:
            formatted_data.append(f"=== {dim}维度数据 ===")
            # 处理维度数据的逻辑
            formatted_data.append("")

    return "\n".join(formatted_data)

def get_sheets_data(attr_index, result, column_name_mapping, sheet_name_mapping):
    if attr_index == '全量GMV':
        gmv_columns_order = ["对比期GMV", "当期GMV", "当期GMV占比", "对比期GMV占比", "GMV变化值", "GMV变化率", "GMV贡献度"]
        # other_columns = ['GMV贡献度']
    elif attr_index == '活动GMV':
        gmv_columns_order = ["当期活动GMV", "当期活动GMV占比", "对比期活动GMV", "对比期活动GMV占比", "活动GMV变化值", "活动GMV变化率", "活动GMV贡献度", "消耗当前值", "消耗变化值", "消耗变化率", "消耗贡献度", "ROI当前值", "ROI变化值", "ROI变化率"]
    sheets_data = {}
    for sheet_name, df in result.items():
        print(f"处理sheet：{sheet_name}, 列：{df.columns.tolist()}")
        
        if attr_index == '活动GMV':
            # 为活动GMV添加前缀，并重命名占比字段
            df.rename(columns={
                'gmv_target': 'activity_gmv_target',
                'gmv_base': 'activity_gmv_base',
                'gmv_change': 'activity_gmv_change',
                'gmv_change_ratio': 'activity_gmv_change_ratio',
                'gmv_contribution_to_total': 'activity_gmv_contribution_to_total',
                'target_ratio': 'activity_target_ratio',
                'base_ratio': 'activity_base_ratio'
            }, inplace=True)
        # 删除对零售商的限制
        # if sheet_name == 'vender_name':
        #     df = df[df["零售商"].isin(allowed_values)]
        df.rename(columns=column_name_mapping, inplace=True)
        
        dimension_columns = [col for col in ["子品牌", "省份", "城市", "平台","零售商", "商品", "券机制", "券门槛", "优惠力度"] if col in df.columns]
        gmv_columns = [col for col in gmv_columns_order if col in df.columns]
        
        # 特别处理商品表的UPC列
        if sheet_name == 'product_name' and 'UPC' in df.columns:
            # 强制将UPC列添加到最终列顺序
            final_column_order = dimension_columns + ['UPC'] + gmv_columns
            print(f"商品表包含UPC列，添加到列顺序中")
        else:
            final_column_order = dimension_columns + gmv_columns
            
        print(f"最终列顺序：{final_column_order}")
        
        # 确保所有列都存在于DataFrame中
        existing_columns = [col for col in final_column_order if col in df.columns]
        df = df[existing_columns]
        
        if attr_index == '全量GMV':
            if "GMV变化率" in df.columns and not df["GMV变化率"].empty and df["GMV变化率"].notna().any():
                df["GMV变化率"] = df["GMV变化率"].apply(lambda x: f"+{x * 100:.2f}%" if x > 0 else f"{x * 100:.2f}%" )
            else:
                df['GMV变化率'] = df['GMV变化率'].replace('nan%', '-')
            df["GMV贡献度"] = df["GMV贡献度"].apply(
                lambda x: f"+{x}" if not x.startswith('-') and x != "0%" else x
            )
            df['GMV变化率'] = df['GMV变化率'].replace('nan%', '-')
        elif attr_index == '活动GMV':
            if "活动GMV变化率" in df.columns:
                print("活动变化率",df["活动GMV变化率"])
            if "活动GMV变化率" in df.columns and not df["活动GMV变化率"].empty and df["活动GMV变化率"].notna().any():
                df["活动GMV变化率"] = df["活动GMV变化率"].apply(
                    lambda x: f"+{x * 100:.2f}%" if x > 0 else f"{x * 100:.2f}%"
                )
            else:
                df['活动GMV变化率'] = df['活动GMV变化率'].replace('nan%', '-')
            df["活动GMV贡献度"] = df["活动GMV贡献度"].apply(
                lambda x: f"+{x}" if not x.startswith('-') and x != "0%" else x
            )
            df["消耗贡献度"] = df["消耗贡献度"].apply(
                lambda x: f"+{x}" if not x.startswith('-') and x != "0%" else x
            )
            if "消耗变化率" in df.columns and not df["消耗变化率"].empty and df["消耗变化率"].notna().any():
                df["消耗变化率"] = df["消耗变化率"].apply(
                    lambda x: f"+{x * 100:.2f}%" if x > 0 else f"{x * 100:.2f}%"
                )
            else:
                df['消耗变化率'] = df['消耗变化率'].replace('nan%', '-')
            if "ROI变化率" in df.columns and not df["ROI变化率"].empty and df["ROI变化率"].notna().any():
                df["ROI变化率"] = df["ROI变化率"].apply(
                    lambda x: '-' if x == '-' else (f"+{x * 100:.2f}%" if x > 0 else f"{x * 100:.2f}%")
                )
            else:
                df['ROI变化率'] = df['ROI变化率'].replace('nan%', '-')
            df['消耗变化率'] = df['消耗变化率'].replace('nan%', '-')
            df['ROI变化值'] = df['ROI变化值'].replace('nan', '-')
            df['ROI变化率'] = df['ROI变化率'].replace('nan', '-')
            df['活动GMV变化率'] = df['活动GMV变化率'].replace('nan%', '-')

        df = df.fillna('-')
        data_dict = [{col: row[col] for col in existing_columns} for row in df.to_dict(orient="records")]
        mapped_sheet_name = sheet_name_mapping.get(sheet_name, sheet_name)
        sheets_data[mapped_sheet_name] = data_dict
        
        # 打印生成的JSON数据(只打印前2条记录用于调试)
        if sheet_name == 'product_name':
            sample_data = sheets_data[mapped_sheet_name][:2] if sheets_data[mapped_sheet_name] else []
            print(f"商品sheet生成的JSON数据示例：{sample_data}")
            
    return sheets_data



def get_provinces_for_single_date_gmv(tar_date, base_date, brand, sub_brand, city,vender, coupon_mechanism, coupon_threshold, coupon_discount,platform,upc):
    sql = f"""
            select province from {get_gmv_table(brand)} 
            where (ds = '{tar_date}' or ds = '{base_date}')
            and brand_filter_condition = '{brand}'
            {build_multi_select_condition('sub_brand', sub_brand)}
            {build_multi_select_condition('standard_city', city)}
            {build_multi_select_condition('vender_name', vender)}
            {build_multi_select_condition('coupon_name', coupon_mechanism)}
            {build_multi_select_condition('coupon_threshold', coupon_threshold)}
            {build_multi_select_condition('coupon_discount', coupon_discount)}
            {build_multi_select_condition('platform', platform)}
            {build_multi_select_condition('upc', upc)}
            group by province;
        """
    df = get_row_data(sql)
    print(sql)
    return df
def get_provinces_for_single_date_activity(tar_date, base_date, brand, sub_brand, city,vender, coupon_mechanism, coupon_threshold, coupon_discount,platform,upc):
    trim_tar_date = tar_date.replace('-', '')
    trim_base_date = base_date.replace('-', '')
    sql = f"""
            select province from {get_activity_table(brand)} 
            where (ds = '{trim_tar_date}' or ds = '{trim_base_date}')
            and brand = '{brand}'
            {build_multi_select_condition('sub_brand', sub_brand)}
            {build_multi_select_condition('city', city)}
            {build_multi_select_condition('vender_name', vender)}
            {build_multi_select_condition('coupon_name', coupon_mechanism)}
            {build_multi_select_condition('coupon_threshold', coupon_threshold)}
            {build_multi_select_condition('coupon_discount', coupon_discount)}
            {build_multi_select_condition('platform', platform)}
            {build_multi_select_condition('upc', upc)}
            group by province;
        """
    df = get_row_data(sql)
    return df




def get_provinces_for_date_range_activity(tar_start_date, tar_end_date, base_start_date, base_end_date, brand, sub_brand, city,vender, coupon_mechanism, coupon_threshold, coupon_discount,platform,upc):
    trim_tar_start_date = tar_start_date.replace('-', '')
    trim_tar_end_date = tar_end_date.replace('-', '')
    trim_base_start_date = base_start_date.replace('-', '')
    trim_base_end_date = base_end_date.replace('-', '')
    sql = f"""
            select province from {get_activity_table(brand)} 
            where (ds between '{trim_tar_start_date}' and '{trim_tar_end_date}' or ds between '{trim_base_start_date}' and '{trim_base_end_date}')
            and brand = '{brand}'
            {build_multi_select_condition('sub_brand', sub_brand)}
            {build_multi_select_condition('city', city)}
            {build_multi_select_condition('vender_name', vender)}
            {build_multi_select_condition('coupon_name', coupon_mechanism)}
            {build_multi_select_condition('coupon_threshold', coupon_threshold)}
            {build_multi_select_condition('coupon_discount', coupon_discount)}
            {build_multi_select_condition('platform', platform)}
            {build_multi_select_condition('upc', upc)}
            group by province;
        """
    df = get_row_data(sql)
    return df


def get_provinces_for_date_range_gmv(tar_start_date, tar_end_date, base_start_date, base_end_date, brand, sub_brand, city,vender, coupon_mechanism, coupon_threshold, coupon_discount,platform,upc):
    sql = f"""
            select province from {get_gmv_table(brand)}
            where (ds between '{tar_start_date}' and '{tar_end_date}' or ds between '{base_start_date}' and '{base_end_date}')
            and brand_filter_condition = '{brand}'
            {build_multi_select_condition('sub_brand', sub_brand)}
            {build_multi_select_condition('standard_city', city)}
            {build_multi_select_condition('vender_name', vender)}
            {build_multi_select_condition('coupon_name', coupon_mechanism)}
            {build_multi_select_condition('coupon_threshold', coupon_threshold)}
            {build_multi_select_condition('coupon_discount', coupon_discount)}
            {build_multi_select_condition('platform', platform)}
            {build_multi_select_condition('upc', upc)}
            group by province;
        """
    df = get_row_data(sql)
    return df





def get_sub_brands_for_single_date_gmv(tar_date, base_date, brand, province, city, vender, coupon_mechanism, coupon_threshold, coupon_discount,platform,upc):
    sql = f"""
            select sub_brand from {get_gmv_table(brand)}
            where (ds = '{tar_date}' or ds = '{base_date}')
            and brand_filter_condition = '{brand}'
            {build_multi_select_condition('province', province)}
            {build_multi_select_condition('standard_city', city)}
            {build_multi_select_condition('vender_name', vender)}
            {build_multi_select_condition('coupon_name', coupon_mechanism)}
            {build_multi_select_condition('coupon_threshold', coupon_threshold)}
            {build_multi_select_condition('coupon_discount', coupon_discount)}
            {build_multi_select_condition('platform', platform)}
            {build_multi_select_condition('upc', upc)}
            group by sub_brand;
        """
    print(sql)
    df = get_row_data(sql)
    return df

def get_sub_brands_for_date_range_gmv(tar_start_date, tar_end_date, base_start_date, base_end_date, brand, province, city, vender, coupon_mechanism, coupon_threshold, coupon_discount,platform,upc):
    sql = f"""
            select sub_brand from {get_gmv_table(brand)}
            where (ds between '{tar_start_date}' and '{tar_end_date}' or ds between '{base_start_date}' and '{base_end_date}')
            and brand_filter_condition = '{brand}'
            {build_multi_select_condition('province', province)}
            {build_multi_select_condition('standard_city', city)}
            {build_multi_select_condition('vender_name', vender)}
            {build_multi_select_condition('coupon_name', coupon_mechanism)}
            {build_multi_select_condition('coupon_threshold', coupon_threshold)}
            {build_multi_select_condition('coupon_discount', coupon_discount)}
            {build_multi_select_condition('platform', platform)}
            {build_multi_select_condition('upc', upc)}
            group by sub_brand;
        """
    print(sql)
    df = get_row_data(sql)
    return df


def get_sub_brands_for_single_date_activity(tar_date, base_date, brand, province, city, vender, coupon_mechanism, coupon_threshold, coupon_discount,platform,upc):
    trim_tar_date = tar_date.replace('-', '')
    trim_base_date = base_date.replace('-', '')
    sql = f"""
            select sub_brand from {get_activity_table(brand)} 
            where (ds = '{trim_tar_date}' or ds = '{trim_base_date}')
            and brand = '{brand}'
            {build_multi_select_condition('province', province)}
            {build_multi_select_condition('city', city)}
            {build_multi_select_condition('vender_name', vender)}
            {build_multi_select_condition('coupon_name', coupon_mechanism)}
            {build_multi_select_condition('coupon_threshold', coupon_threshold)}
            {build_multi_select_condition('coupon_discount', coupon_discount)}
            {build_multi_select_condition('platform', platform)}
            {build_multi_select_condition('upc', upc)}
            group by sub_brand;
        """
    df = get_row_data(sql)
    print(sql)
    return df


def get_sub_brands_for_date_range_activity(tar_start_date, tar_end_date, base_start_date, base_end_date, brand, province, city, vender, coupon_mechanism, coupon_threshold, coupon_discount,platform,upc):
    trim_tar_start_date = tar_start_date.replace('-', '')
    trim_tar_end_date = tar_end_date.replace('-', '')
    trim_base_start_date = base_start_date.replace('-', '')
    trim_base_end_date = base_end_date.replace('-', '')
    sql = f"""
            select sub_brand from {get_activity_table(brand)} 
            where (ds between '{trim_tar_start_date}' and '{trim_tar_end_date}' or ds between '{trim_base_start_date}' and '{trim_base_end_date}')
            and brand = '{brand}'
            {build_multi_select_condition('province', province)}
            {build_multi_select_condition('city', city)}
            {build_multi_select_condition('vender_name', vender)}
            {build_multi_select_condition('coupon_name', coupon_mechanism)}
            {build_multi_select_condition('coupon_threshold', coupon_threshold)}
            {build_multi_select_condition('coupon_discount', coupon_discount)}
            {build_multi_select_condition('platform', platform)}
            {build_multi_select_condition('upc', upc)}
            group by sub_brand;
        """
    df = get_row_data(sql)
    return df


def get_retailers_for_single_date_gmv(tar_date, base_date, brand, sub_brand,  province, city,coupon_mechanism, coupon_threshold, coupon_discount,platform,upc):
    sql = f"""
            select vender_name from {get_gmv_table(brand)}
            where (ds = '{tar_date}' or ds = '{base_date}')
            and brand_filter_condition = '{brand}'
            {build_multi_select_condition('sub_brand', sub_brand)}
            {build_multi_select_condition('standard_city', city)}
            {build_multi_select_condition('province', province)}
            {build_multi_select_condition('coupon_name', coupon_mechanism)}
            {build_multi_select_condition('coupon_threshold', coupon_threshold)}
            {build_multi_select_condition('coupon_discount', coupon_discount)}    
            {build_multi_select_condition('platform', platform)}
            {build_multi_select_condition('upc', upc)}
            group by vender_name;
        """
    df = get_row_data(sql)
    return df


def get_retailers_for_date_range_gmv(tar_start_date, tar_end_date, base_start_date, base_end_date, brand, sub_brand,  province,city, coupon_mechanism, coupon_threshold, coupon_discount,platform,upc):
    sql = f"""
            select vender_name from {get_gmv_table(brand)}
            where (ds between '{tar_start_date}' and '{tar_end_date}' or ds between '{base_start_date}' and '{base_end_date}')
            and brand_filter_condition = '{brand}'
            {build_multi_select_condition('sub_brand', sub_brand)}
            {build_multi_select_condition('standard_city', city)}
            {build_multi_select_condition('province', province)}
            {build_multi_select_condition('coupon_name', coupon_mechanism)}
            {build_multi_select_condition('coupon_threshold', coupon_threshold)}
            {build_multi_select_condition('coupon_discount', coupon_discount)}
            {build_multi_select_condition('platform', platform)}
            {build_multi_select_condition('upc', upc)}
            group by vender_name;
        """
    df = get_row_data(sql)
    return df


def get_retailers_for_single_date_activity(tar_date, base_date, brand, sub_brand,  province,city, coupon_mechanism, coupon_threshold, coupon_discount,platform,upc):
    trim_tar_date = tar_date.replace('-', '')
    trim_base_date = base_date.replace('-', '')
    sql = f"""
            select vender_name from {get_activity_table(brand)} 
            where (ds = '{trim_tar_date}' or ds = '{trim_base_date}')
            and brand = '{brand}'
            {build_multi_select_condition('sub_brand', sub_brand)}
            {build_multi_select_condition('city', city)}
            {build_multi_select_condition('province', province)}
            {build_multi_select_condition('coupon_name', coupon_mechanism)}
            {build_multi_select_condition('coupon_threshold', coupon_threshold)}
            {build_multi_select_condition('coupon_discount', coupon_discount)}
            {build_multi_select_condition('platform', platform)}
            {build_multi_select_condition('upc', upc)}
            group by vender_name;
        """
    df = get_row_data(sql)
    return df


def get_retailers_for_date_range_activity(tar_start_date, tar_end_date, base_start_date, base_end_date, brand, sub_brand,  province,city, coupon_mechanism, coupon_threshold, coupon_discount,platform,upc):
    trim_tar_start_date = tar_start_date.replace('-', '')
    trim_tar_end_date = tar_end_date.replace('-', '')
    trim_base_start_date = base_start_date.replace('-', '')
    trim_base_end_date = base_end_date.replace('-', '')
    sql = f"""
            select vender_name from {get_activity_table(brand)} 
            where (ds between '{trim_tar_start_date}' and '{trim_tar_end_date}' or ds between '{trim_base_start_date}' and '{trim_base_end_date}')
            and brand = '{brand}'
            {build_multi_select_condition('sub_brand', sub_brand)}
            {build_multi_select_condition('city', city)}
            {build_multi_select_condition('province', province)}
            {build_multi_select_condition('coupon_name', coupon_mechanism)}
            {build_multi_select_condition('coupon_threshold', coupon_threshold)}
            {build_multi_select_condition('coupon_discount', coupon_discount)}
            {build_multi_select_condition('platform', platform)}
            {build_multi_select_condition('upc', upc)}
            group by vender_name;
        """
    df = get_row_data(sql)
    return df


def get_coupon_mechanisms_for_single_date_activity(tar_date, base_date,brand, sub_brand,  province, city,vender, coupon_threshold, coupon_discount,platform,upc):
    trim_tar_date = tar_date.replace('-', '')
    trim_base_date = base_date.replace('-', '')
    sql = f"""
            select coupon_name from {get_activity_table(brand)} 
            where (ds = '{trim_tar_date}' or ds = '{trim_base_date}')
            and brand = '{brand}'
            {build_multi_select_condition('sub_brand', sub_brand)}
            {build_multi_select_condition('city', city)}
            {build_multi_select_condition('province', province)}
            {build_multi_select_condition('vender_name', vender)}
            {build_multi_select_condition('coupon_threshold', coupon_threshold)}
            {build_multi_select_condition('coupon_discount', coupon_discount)}
            {build_multi_select_condition('platform', platform)}
            {build_multi_select_condition('upc', upc)}
            group by coupon_name;
        """
    print(sql)
    df = get_row_data(sql)
    return df


def get_coupon_mechanisms_for_date_range_activity(tar_start_date, tar_end_date, base_start_date, base_end_date, brand, sub_brand,  province,city, vender, coupon_threshold, coupon_discount,platform,upc):
    trim_tar_start_date = tar_start_date.replace('-', '')
    trim_tar_end_date = tar_end_date.replace('-', '')
    trim_base_start_date = base_start_date.replace('-', '')
    trim_base_end_date = base_end_date.replace('-', '')
    sql = f"""
            select coupon_name from {get_activity_table(brand)} 
            where (ds between '{trim_tar_start_date}' and '{trim_tar_end_date}' or ds between '{trim_base_start_date}' and '{trim_base_end_date}')
            and brand = '{brand}'
            {build_multi_select_condition('sub_brand', sub_brand)}
            {build_multi_select_condition('city', city)}
            {build_multi_select_condition('province', province)}
            {build_multi_select_condition('vender_name', vender)}
            {build_multi_select_condition('coupon_threshold', coupon_threshold)}
            {build_multi_select_condition('coupon_discount', coupon_discount)}
            {build_multi_select_condition('platform', platform)}
            {build_multi_select_condition('upc', upc)}
            group by coupon_name;
        """
    df = get_row_data(sql)
    return df


def get_platform_for_single_date_gmv(tar_date, base_date, brand, sub_brand,  province, city,vender, coupon_mechanism, coupon_threshold, coupon_discount,upc):
    sql = f"""
            select platform from {get_gmv_table(brand)}
            where (ds = '{tar_date}' or ds = '{base_date}')
            and brand_filter_condition = '{brand}'
            {build_multi_select_condition('vender_name', vender)}
            {build_multi_select_condition('sub_brand', sub_brand)}
            {build_multi_select_condition('standard_city', city)}
            {build_multi_select_condition('province', province)}
            {build_multi_select_condition('coupon_name', coupon_mechanism)}
            {build_multi_select_condition('coupon_threshold', coupon_threshold)}
            {build_multi_select_condition('coupon_discount', coupon_discount)}
            {build_multi_select_condition('upc', upc)}
            group by platform;
        """
    df = get_row_data(sql)
    return df
def get_platform_for_date_range_gmv(tar_start_date, tar_end_date, base_start_date, base_end_date, brand, sub_brand,  province,city,vender, coupon_mechanism, coupon_threshold, coupon_discount,upc):
    sql = f"""
            select platform from {get_gmv_table(brand)}
            where (ds between '{tar_start_date}' and '{tar_end_date}' or ds between '{base_start_date}' and '{base_end_date}')
            and brand_filter_condition = '{brand}'
            {build_multi_select_condition('vender_name', vender)}
            {build_multi_select_condition('sub_brand', sub_brand)}
            {build_multi_select_condition('standard_city', city)}
            {build_multi_select_condition('province', province)}
            {build_multi_select_condition('coupon_name', coupon_mechanism)}
            {build_multi_select_condition('coupon_threshold', coupon_threshold)}
            {build_multi_select_condition('coupon_discount', coupon_discount)}
            {build_multi_select_condition('upc', upc)}
            group by platform;
        """
    df = get_row_data(sql)
    return df

def get_platform_for_single_date_activity(tar_date, base_date, brand, sub_brand,  province,city, vender, coupon_mechanism, coupon_threshold, coupon_discount,upc):
    trim_tar_date = tar_date.replace('-', '')
    trim_base_date = base_date.replace('-', '')
    sql = f"""
            select platform from {get_activity_table(brand)}
            where (ds = '{trim_tar_date}' or ds = '{trim_base_date}')
            and brand = '{brand}'
            {build_multi_select_condition('sub_brand', sub_brand)}
            {build_multi_select_condition('city', city)}
            {build_multi_select_condition('province', province)}
            {build_multi_select_condition('vender_name', vender)}
            {build_multi_select_condition('coupon_name', coupon_mechanism)}
            {build_multi_select_condition('coupon_threshold', coupon_threshold)}
            {build_multi_select_condition('coupon_discount', coupon_discount)}
            {build_multi_select_condition('upc', upc)}
            group by platform;
        """
    df = get_row_data(sql)
    return df

def get_platform_for_date_range_activity(tar_start_date, tar_end_date, base_start_date, base_end_date, brand, sub_brand,  province, city,vender, coupon_mechanism, coupon_threshold, coupon_discount,upc):
    trim_tar_start_date = tar_start_date.replace('-', '')
    trim_tar_end_date = tar_end_date.replace('-', '')
    trim_base_start_date = base_start_date.replace('-', '')
    trim_base_end_date = base_end_date.replace('-', '')
    sql = f"""
            select platform from {get_activity_table(brand)}
            where (ds between '{trim_tar_start_date}' and '{trim_tar_end_date}' or ds between '{trim_base_start_date}' and '{trim_base_end_date}')
            and brand = '{brand}'
            {build_multi_select_condition('sub_brand', sub_brand)}
            {build_multi_select_condition('city', city)}
            {build_multi_select_condition('province', province)}
            {build_multi_select_condition('vender_name', vender)}
            {build_multi_select_condition('coupon_name', coupon_mechanism)}
            {build_multi_select_condition('coupon_threshold', coupon_threshold)}
            {build_multi_select_condition('coupon_discount', coupon_discount)}
            {build_multi_select_condition('upc', upc)}
            group by platform;
        """
    df = get_row_data(sql)
    return df

def get_products_for_single_date_gmv(tar_date, base_date, brand, sub_brand,  province,city, vender, coupon_mechanism, coupon_threshold, coupon_discount, platform):
    sql = f"""
            select product_name, upc from {get_gmv_table(brand)}
            where (ds = '{tar_date}' or ds = '{base_date}')
            and brand_filter_condition = '{brand}'
            {build_multi_select_condition('sub_brand', sub_brand)}
            {build_multi_select_condition('standard_city', city)}
            {build_multi_select_condition('province', province)}
            {build_multi_select_condition('vender_name', vender)}
            {build_multi_select_condition('coupon_name', coupon_mechanism)}
            {build_multi_select_condition('coupon_threshold', coupon_threshold)}
            {build_multi_select_condition('coupon_discount', coupon_discount)}
            {build_multi_select_condition('platform', platform)}
            group by product_name, upc;
        """
    df = get_row_data(sql)
    return df

def get_products_for_date_range_gmv(tar_start_date, tar_end_date, base_start_date, base_end_date, brand, sub_brand,  province,city, vender, coupon_mechanism, coupon_threshold, coupon_discount, platform):
    sql = f"""
            select product_name, upc from {get_gmv_table(brand)}
            where (ds between '{tar_start_date}' and '{tar_end_date}' or ds between '{base_start_date}' and '{base_end_date}')
            and brand_filter_condition = '{brand}'
            {build_multi_select_condition('sub_brand', sub_brand)}
            {build_multi_select_condition('standard_city', city)}
            {build_multi_select_condition('province', province)}
            {build_multi_select_condition('vender_name', vender)}
            {build_multi_select_condition('coupon_name', coupon_mechanism)}
            {build_multi_select_condition('coupon_threshold', coupon_threshold)}
            {build_multi_select_condition('coupon_discount', coupon_discount)}
            {build_multi_select_condition('platform', platform)}
            group by product_name, upc;
        """
    df = get_row_data(sql)
    return df

def get_products_for_single_date_activity(tar_date, base_date, brand, sub_brand, city, province, vender, coupon_mechanism, coupon_threshold, coupon_discount, platform):
    table_name = get_activity_table(brand)
    datetime.strptime(tar_date, "%Y%m%d")
    trim_tar_date = tar_date.replace('-', '')
    trim_base_date = base_date.replace('-', '')
    sql = f"""
            select product_name, upc from {table_name}
            where (ds = '{trim_tar_date}' or ds = '{trim_base_date}')
            and brand = '{brand}'
            {build_multi_select_condition('sub_brand', sub_brand)}
            {build_multi_select_condition('city', city)}
            {build_multi_select_condition('province', province)}
            {build_multi_select_condition('vender_name', vender)}
            {build_multi_select_condition('coupon_name', coupon_mechanism)}
            {build_multi_select_condition('coupon_threshold', coupon_threshold)}
            {build_multi_select_condition('coupon_discount', coupon_discount)}
            {build_multi_select_condition('platform', platform)}
            group by product_name, upc;
        """
    df = get_row_data(sql)
    return df

def get_products_for_date_range_activity(tar_start_date, tar_end_date, base_start_date, base_end_date, brand, sub_brand,  province,city, vender, coupon_mechanism, coupon_threshold, coupon_discount, platform):
    table_name = get_activity_table(brand)
    datetime.strptime(tar_start_date, "%Y%m%d")
    datetime.strptime(tar_end_date, "%Y%m%d")
    datetime.strptime(base_start_date, "%Y%m%d")
    datetime.strptime(base_end_date, "%Y%m%d")
    
    trim_tar_start_date = tar_start_date.replace('-', '')
    trim_tar_end_date = tar_end_date.replace('-', '')
    trim_base_start_date = base_start_date.replace('-', '')
    trim_base_end_date = base_end_date.replace('-', '')
    
    sql = f"""
            select product_name, upc from {table_name}
            where (ds between '{trim_tar_start_date}' and '{trim_tar_end_date}' or ds between '{trim_base_start_date}' and '{trim_base_end_date}')
            and brand = '{brand}'
            {build_multi_select_condition('sub_brand', sub_brand)}
            {build_multi_select_condition('province', province)}
            {build_multi_select_condition('city', city)}
            {build_multi_select_condition('vender_name', vender)}
            {build_multi_select_condition('coupon_name', coupon_mechanism)}
            {build_multi_select_condition('coupon_threshold', coupon_threshold)}
            {build_multi_select_condition('coupon_discount', coupon_discount)}
            {build_multi_select_condition('platform', platform)}
            group by product_name, upc;
        """
    df = get_row_data(sql)
    return df

def get_historical_gmv_changes(brand, compare_type, date_type=None, days=100, sub_brand="全部",  province="全部", city="全部",vender="全部", platform="全部", upc="全部", gmv_base = 0, gmv_target = 0, tar_start_date=None, tar_end_date=None, base_start_date=None, base_end_date=None):
    """
    获取历史GMV波动数据

    参数:
        brand: 品牌名称
        compare_type: 对比类型 ("日环比", "周同比", "月同比", "年同比", "年同比(按周)", "月环比")
        date_type: 日期类型 ("日", "月")，用于确定时间粒度
        days: 历史参数，已不再使用
        sub_brand: 子品牌筛选
        province: 省份筛选
        vender: 零售商筛选
        platform: 平台筛选
        upc: 商品UPC筛选
        tar_start_date: 目标开始日期（日期范围模式）
        tar_end_date: 目标结束日期（日期范围模式）
        base_start_date: 对比开始日期（日期范围模式）
        base_end_date: 对比结束日期（日期范围模式）

    返回:
        历史GMV波动数据列表
    """
    try:
        # 构建基础筛选条件
        filter_conditions = [f"brand_filter_condition = '{brand}'"]
        
        # 使用多选条件构建函数
        sub_brand_condition = build_multi_select_condition('sub_brand', sub_brand)
        if sub_brand_condition:
            clean_condition = sub_brand_condition[4:] if sub_brand_condition.startswith('and ') else sub_brand_condition
            filter_conditions.append(clean_condition)

        province_condition = build_multi_select_condition('province', province)
        if province_condition:
            clean_condition = province_condition[4:] if province_condition.startswith('and ') else province_condition
            filter_conditions.append(clean_condition)

        city_condition = build_multi_select_condition('standard_city', city)
        if city_condition:
            clean_condition = city_condition[4:] if city_condition.startswith('and ') else city_condition
            filter_conditions.append(clean_condition)

        vender_condition = build_multi_select_condition('vender_name', vender)
        if vender_condition:
            clean_condition = vender_condition[4:] if vender_condition.startswith('and ') else vender_condition
            filter_conditions.append(clean_condition)

        platform_condition = build_multi_select_condition('platform', platform)
        if platform_condition:
            clean_condition = platform_condition[4:] if platform_condition.startswith('and ') else platform_condition
            filter_conditions.append(clean_condition)

        upc_condition = build_multi_select_condition('upc', upc)
        if upc_condition:
            clean_condition = upc_condition[4:] if upc_condition.startswith('and ') else upc_condition
            filter_conditions.append(clean_condition)
        
        # 将所有筛选条件连接为一个字符串
        filter_condition = " AND ".join(filter_conditions)
        
        
        if date_type == "月" and compare_type == "月环比":
            # 月级别月环比数据 - 限制查询范围以避免分区限制
            sql = f"""
            set hg_experimental_foreign_table_max_partition_limit = 800;
            WITH base_data AS (
                SELECT
                    TO_CHAR(TO_DATE(ds, 'yyyy-mm-dd'), 'YYYY-MM') AS month_key,
                    SUM(gmv) AS gmv
                FROM
                    {get_gmv_table(brand)}
                WHERE
                    TO_DATE(ds, 'yyyy-mm-dd') >= TO_DATE('2023-12-01', 'yyyy-mm-dd')
                    AND TO_DATE(ds, 'yyyy-mm-dd') <= CURRENT_DATE
                    AND {filter_condition}
                GROUP BY
                    TO_CHAR(TO_DATE(ds, 'yyyy-mm-dd'), 'YYYY-MM')
            ),
            current_data AS (
                SELECT
                    month_key,
                    gmv
                FROM
                    base_data
            ),
            prev_month_data AS (
                SELECT
                    TO_CHAR(TO_DATE(month_key || '-01', 'YYYY-MM-DD') + INTERVAL '1 month', 'YYYY-MM') AS month_key,
                    gmv AS gmv_prev_month
                FROM
                    base_data
            )
            SELECT
                c.month_key AS date,
                c.gmv AS gmv_target,
                pm.gmv_prev_month AS gmv_base,
                (c.gmv - pm.gmv_prev_month) AS gmv_change,
                CASE
                    WHEN pm.gmv_prev_month = 0 OR pm.gmv_prev_month IS NULL THEN NULL
                    ELSE (c.gmv - pm.gmv_prev_month) * 1.0 / pm.gmv_prev_month
                END AS gmv_change_rate
            FROM
                current_data c
            LEFT JOIN
                prev_month_data pm
            ON
                c.month_key = pm.month_key
            ORDER BY
                c.month_key
            """
        elif compare_type == "日环比":
            # 日级别日环比数据（保持原有逻辑）
            sql = f"""
            set hg_experimental_foreign_table_max_partition_limit = 800;
            WITH base_data AS (
                SELECT
                    TO_DATE(ds, 'yyyy-mm-dd') AS dt,
                    SUM(gmv) AS gmv
                FROM
                    {get_gmv_table(brand)}
                WHERE
                    TO_DATE(ds, 'yyyy-mm-dd') >= TO_DATE('2024-12-31', 'yyyy-mm-dd')
                    AND TO_DATE(ds, 'yyyy-mm-dd') <= CURRENT_DATE
                    AND {filter_condition}
                GROUP BY
                    ds
            ),
            current_data AS (
                SELECT
                    dt,
                    gmv
                FROM
                    base_data
            ),
            prev_day_data AS (
                SELECT
                    dt + INTERVAL '1 day' AS dt,
                    gmv AS gmv_prev_day
                FROM
                    base_data
            )
            SELECT
                TO_CHAR(c.dt, 'YYYYMMDD') AS date,
                c.gmv AS gmv_target,
                pd.gmv_prev_day AS gmv_base,
                (c.gmv - pd.gmv_prev_day) AS gmv_change,
                CASE
                    WHEN pd.gmv_prev_day = 0 OR pd.gmv_prev_day IS NULL THEN NULL
                    ELSE (c.gmv - pd.gmv_prev_day) * 1.0 / pd.gmv_prev_day
                END AS gmv_change_rate
            FROM
                current_data c
            LEFT JOIN
                prev_day_data pd
            ON
                c.dt = pd.dt
            ORDER BY
                c.dt
            """
        elif compare_type == "周同比":
            sql = f"""
            set hg_experimental_foreign_table_max_partition_limit = 800;
            WITH base_data AS (
                SELECT
                    TO_DATE(ds, 'yyyy-mm-dd') AS dt,
                    SUM(gmv) AS gmv
                FROM
                    {get_gmv_table(brand)} 
                WHERE
                    TO_DATE(ds, 'yyyy-mm-dd') >= TO_DATE('2024-12-25', 'yyyy-mm-dd')
                    AND TO_DATE(ds, 'yyyy-mm-dd') <= CURRENT_DATE
                    AND {filter_condition}
                GROUP BY
                    ds
            ),
            current_data AS (
                SELECT
                    dt,
                    gmv
                FROM
                    base_data
            ),
            last_week_data AS (
                SELECT
                    dt + INTERVAL '7 days' AS dt,
                    gmv AS gmv_last_week
                FROM
                    base_data
            )
            SELECT
                TO_CHAR(c.dt, 'YYYYMMDD') AS date,
                c.gmv AS gmv_target,
                lw.gmv_last_week AS gmv_base,
                (c.gmv - lw.gmv_last_week) AS gmv_change,
                CASE 
                    WHEN lw.gmv_last_week = 0 OR lw.gmv_last_week IS NULL THEN NULL
                    ELSE (c.gmv - lw.gmv_last_week) * 1.0 / lw.gmv_last_week
                END AS gmv_change_rate
            FROM
                current_data c
            LEFT JOIN
                last_week_data lw
            ON
                c.dt = lw.dt
            ORDER BY
                c.dt
            """
        elif compare_type == "月同比":
            sql = f"""
            set hg_experimental_foreign_table_max_partition_limit = 800;
            WITH base_data AS (
                SELECT
                    TO_DATE(ds, 'yyyy-mm-dd') AS dt,
                    SUM(gmv) AS gmv
                FROM
                    {get_gmv_table(brand)} 
                WHERE
                    TO_DATE(ds, 'yyyy-mm-dd') >= TO_DATE('2024-12-01', 'yyyy-mm-dd')
                    AND TO_DATE(ds, 'yyyy-mm-dd') <= CURRENT_DATE
                    AND {filter_condition}
                GROUP BY
                    ds
            ),
            current_data AS (
                SELECT
                    dt,
                    gmv
                FROM
                    base_data
            ),
            last_month_data AS (
                SELECT
                    dt + INTERVAL '1 month' AS dt,
                    gmv AS gmv_last_month
                FROM
                    base_data
            )
            SELECT
                TO_CHAR(c.dt, 'YYYYMMDD') AS date,
                c.gmv AS gmv_target,
                lm.gmv_last_month AS gmv_base,
                (c.gmv - lm.gmv_last_month) AS gmv_change,
                CASE 
                    WHEN lm.gmv_last_month = 0 OR lm.gmv_last_month IS NULL THEN NULL
                    ELSE (c.gmv - lm.gmv_last_month) * 1.0 / lm.gmv_last_month
                END AS gmv_change_rate
            FROM
                current_data c
            LEFT JOIN
                last_month_data lm
            ON
                c.dt = lm.dt
            ORDER BY
                c.dt
            """
        elif compare_type == "年同比":
            sql = f"""
            set hg_experimental_foreign_table_max_partition_limit = 800;
            WITH current_year_data AS (
                SELECT
                    TO_DATE(ds, 'yyyy-mm-dd') AS dt,
                    SUM(gmv) AS gmv
                FROM
                    {get_gmv_table(brand)} 
                WHERE
                    ds >= '2025-01-01'
                    AND ds <= TO_CHAR(CURRENT_DATE - INTERVAL '1 day', 'YYYY-MM-DD')
                    AND {filter_condition}
                GROUP BY
                    ds
            ),
            last_year_data AS (
                SELECT
                    TO_DATE(ds, 'yyyy-mm-dd') AS dt,
                    SUM(gmv) AS gmv
                FROM
                    {get_gmv_table(brand)} 
                WHERE
                    ds >= '2024-01-01'
                    AND ds <= TO_CHAR(CURRENT_DATE - INTERVAL '1 day' - INTERVAL '1 year', 'YYYY-MM-DD')
                    AND {filter_condition}
                GROUP BY
                    ds
            ),
            -- 计算日期映射关系（去年同日）
            date_mapping AS (
                SELECT 
                    dt AS current_dt,
                    dt - INTERVAL '1 year' AS target_last_year_date
                FROM current_year_data
            ),
            -- 匹配去年同日的数据
            matched_data AS (
                SELECT
                    dm.current_dt,
                    ly.gmv AS gmv_last_year
                FROM
                    date_mapping dm
                LEFT JOIN
                    last_year_data ly
                ON
                    ly.dt = dm.target_last_year_date
            )
            SELECT
                TO_CHAR(c.dt, 'YYYYMMDD') AS date,
                c.gmv AS gmv_target,
                md.gmv_last_year AS gmv_base,
                (c.gmv - md.gmv_last_year) AS gmv_change,
                CASE 
                    WHEN md.gmv_last_year = 0 OR md.gmv_last_year IS NULL THEN NULL
                    ELSE (c.gmv - md.gmv_last_year) * 1.0 / md.gmv_last_year
                END AS gmv_change_rate
            FROM
                current_year_data c
            LEFT JOIN
                matched_data md
            ON
                c.dt = md.current_dt
            ORDER BY
                c.dt
            """
        elif compare_type == "年同比(按周)" or compare_type == "年同比（按周）":
            sql = f"""
            set hg_experimental_foreign_table_max_partition_limit = 800;
            WITH current_year_data AS (
                SELECT
                    TO_DATE(ds, 'yyyy-mm-dd') AS dt,
                    SUM(gmv) AS gmv
                FROM
                    {get_gmv_table(brand)} 
                WHERE
                    ds >= '2025-01-01'
                    AND ds <= TO_CHAR(CURRENT_DATE - INTERVAL '1 day', 'YYYY-MM-DD')
                    AND {filter_condition}
                GROUP BY
                    ds
            ),
            last_year_data AS (
                SELECT
                    TO_DATE(ds, 'yyyy-mm-dd') AS dt,
                    SUM(gmv) AS gmv
                FROM
                    {get_gmv_table(brand)} 
                WHERE
                    ds >= '2024-01-01'
                    AND ds <= TO_CHAR(CURRENT_DATE - INTERVAL '1 day' - INTERVAL '1 year' + INTERVAL '7 days', 'YYYY-MM-DD')
                    AND {filter_condition}
                GROUP BY
                    ds
            ),
            -- 计算日期映射关系（去年同周同日）
            date_mapping AS (
                SELECT 
                    dt AS current_dt,
                    -- 使用ISO周次计算去年同周同日，处理跨年边界
                    CASE 
                        WHEN EXTRACT(week FROM dt) > 52 THEN
                            -- 处理第53周的情况（跨年）
                            dt - INTERVAL '1 year'
                        ELSE
                            -- 正常情况：使用ISO周次计算
                            (DATE_TRUNC('year', dt - INTERVAL '1 year')::date + 
                             (EXTRACT(week FROM dt)::int - 1) * 7 + 
                             (EXTRACT(isodow FROM dt)::int - 1))
                    END AS target_last_year_date
                FROM current_year_data
            ),
            -- 匹配去年同周同日的数据
            matched_data AS (
                SELECT
                    dm.current_dt,
                    ly.gmv AS gmv_last_year
                FROM
                    date_mapping dm
                LEFT JOIN
                    last_year_data ly
                ON
                    ly.dt = dm.target_last_year_date
            )
            SELECT
                TO_CHAR(c.dt, 'YYYYMMDD') AS date,
                c.gmv AS gmv_target,
                md.gmv_last_year AS gmv_base,
                (c.gmv - md.gmv_last_year) AS gmv_change,
                CASE 
                    WHEN md.gmv_last_year = 0 OR md.gmv_last_year IS NULL THEN NULL
                    ELSE (c.gmv - md.gmv_last_year) * 1.0 / md.gmv_last_year
                END AS gmv_change_rate
            FROM
                current_year_data c
            LEFT JOIN
                matched_data md
            ON
                c.dt = md.current_dt
            ORDER BY
                c.dt
            """
        else:
            # 默认使用周同比
            sql = f"""
            set hg_experimental_foreign_table_max_partition_limit = 800;
            WITH base_data AS (
                SELECT
                    TO_DATE(ds, 'yyyy-mm-dd') AS dt,
                    SUM(gmv) AS gmv
                FROM
                    {get_gmv_table(brand)} 
                WHERE
                    TO_DATE(ds, 'yyyy-mm-dd') >= TO_DATE('2024-12-25', 'yyyy-mm-dd')
                    AND TO_DATE(ds, 'yyyy-mm-dd') <= CURRENT_DATE
                    AND {filter_condition}
                GROUP BY
                    ds
            ),
            current_data AS (
                SELECT
                    dt,
                    gmv
                FROM
                    base_data
            ),
            last_week_data AS (
                SELECT
                    dt + INTERVAL '7 days' AS dt,
                    gmv AS gmv_last_week
                FROM
                    base_data
            )
            SELECT
                TO_CHAR(c.dt, 'YYYYMMDD') AS date,
                c.gmv AS gmv_target,
                lw.gmv_last_week AS gmv_base,
                (c.gmv - lw.gmv_last_week) AS gmv_change,
                CASE 
                    WHEN lw.gmv_last_week = 0 OR lw.gmv_last_week IS NULL THEN NULL
                    ELSE (c.gmv - lw.gmv_last_week) * 1.0 / lw.gmv_last_week
                END AS gmv_change_rate
            FROM
                current_data c
            LEFT JOIN
                last_week_data lw
            ON
                c.dt = lw.dt
            ORDER BY
                c.dt
            """
        print(sql)
        # 执行SQL并获取结果
        # 假设这里有一个execute_sql函数，用于执行SQL并返回DataFrame
        df = get_row_data(sql)
        
        # 处理结果并构建返回列表
        historical_changes = []
        for _, row in df.iterrows():
            if pd.notna(row['gmv_change']) and pd.notna(row['gmv_change_rate']):
                historical_changes.append({
                    "date": row['date'],
                    "gmv_change": round(float(row['gmv_change'])),
                    "gmv_change_rate": float(row['gmv_change_rate']) * 100
                })
        
        return historical_changes
    except Exception as e:
        print(f"获取历史GMV波动数据时出错: {str(e)}")
        return []

def get_historical_activity_gmv_changes(brand, compare_type, date_type=None, days=100, sub_brand="全部",  province="全部",city="全部", vender="全部",
                                        coupon_mechanism="全部", coupon_threshold="全部", coupon_discount="全部",
                                        platform="全部", upc="全部", gmv_base = 0, gmv_target = 0, tar_start_date=None, tar_end_date=None, base_start_date=None, base_end_date=None):
    """
    获取历史活动GMV波动数据

    参数:
        brand: 品牌名称
        compare_type: 对比类型 ("日环比", "周同比", "月同比", "年同比", "年同比(按周)", "月环比")
        date_type: 日期类型 ("日", "月")，用于确定时间粒度
        days: 历史参数，已不再使用
        sub_brand: 子品牌筛选
        province: 省份筛选
        vender: 零售商筛选
        coupon_mechanism: 券机制筛选
        coupon_threshold: 券门槛筛选
        coupon_discount: 优惠力度筛选
        platform: 平台筛选
        upc: 商品UPC筛选
        tar_start_date: 目标开始日期（日期范围模式）
        tar_end_date: 目标结束日期（日期范围模式）
        base_start_date: 对比开始日期（日期范围模式）
        base_end_date: 对比结束日期（日期范围模式）

    返回:
        历史活动GMV波动数据列表
    """
    try:
        # 如果是日期范围模式，返回空列表（暂不支持历史数据）
        if tar_start_date and tar_end_date and base_start_date and base_end_date:
            return []
            
        # 构建基础筛选条件
        filter_conditions = [f"brand = '{brand}'"]  # 活动数据
        if sub_brand != "全部":
            filter_conditions.append(f"sub_brand = '{sub_brand}'")
        if province != "全部":
            filter_conditions.append(f"province = '{province}'")
        if city != "全部":
            filter_conditions.append(f"city = '{city}'")
        if vender != "全部":
            filter_conditions.append(f"vender_name = '{vender}'")
        if coupon_mechanism != "全部":
            filter_conditions.append(f"coupon_name = '{coupon_mechanism}'")
        if coupon_threshold != "全部":
            filter_conditions.append(f"coupon_threshold = '{coupon_threshold}'")
        if coupon_discount != "全部":
            filter_conditions.append(f"coupon_discount = '{coupon_discount}'")
        if platform != "全部":
            filter_conditions.append(f"platform = '{platform}'")
        if upc != "全部":
            filter_conditions.append(f"upc = '{upc}'")
        
        # 将所有筛选条件连接为一个字符串
        filter_condition = " AND ".join(filter_conditions)
        
        # 根据日期类型和对比类型构建不同的SQL
        if date_type == "月" and compare_type == "月环比":
            # 月级别月环比数据 - 限制查询范围以避免分区限制
            sql = f"""
            WITH base_data AS (
                SELECT
                    TO_CHAR(TO_DATE(ds, 'yyyymmdd'), 'YYYY-MM') AS month_key,
                    SUM(activity_gmv) AS gmv,
                    SUM(activity_expense) AS expense
                FROM
                    {get_activity_table(brand)}
                WHERE
                    TO_DATE(ds, 'yyyymmdd') >= TO_DATE('2023-12-01', 'yyyy-mm-dd')
                    AND TO_DATE(ds, 'yyyymmdd') <= CURRENT_DATE
                    AND {filter_condition}
                GROUP BY
                    TO_CHAR(TO_DATE(ds, 'yyyymmdd'), 'YYYY-MM')
            ),
            current_data AS (
                SELECT
                    month_key,
                    gmv,
                    expense
                FROM
                    base_data
            ),
            prev_month_data AS (
                SELECT
                    TO_CHAR(TO_DATE(month_key || '-01', 'YYYY-MM-DD') + INTERVAL '1 month', 'YYYY-MM') AS month_key,
                    gmv AS gmv_prev_month,
                    expense AS expense_prev_month
                FROM
                    base_data
            )
            SELECT
                c.month_key AS date,
                c.gmv AS gmv_target,
                pm.gmv_prev_month AS gmv_base,
                (c.gmv - pm.gmv_prev_month) AS gmv_change,
                CASE
                    WHEN pm.gmv_prev_month = 0 OR pm.gmv_prev_month IS NULL THEN NULL
                    ELSE (c.gmv - pm.gmv_prev_month) * 1.0 / pm.gmv_prev_month
                END AS gmv_change_rate,
                c.expense AS expense_target,
                pm.expense_prev_month AS expense_base,
                (c.expense - pm.expense_prev_month) AS expense_change,
                CASE
                    WHEN pm.expense_prev_month = 0 OR pm.expense_prev_month IS NULL THEN NULL
                    ELSE (c.expense - pm.expense_prev_month) * 1.0 / pm.expense_prev_month
                END AS expense_change_rate
            FROM
                current_data c
            LEFT JOIN
                prev_month_data pm
            ON
                c.month_key = pm.month_key
            ORDER BY
                c.month_key
            """
        elif compare_type == "日环比":
            # 日级别日环比数据（保持原有逻辑）
            sql = f"""
            WITH base_data AS (
                SELECT
                    TO_DATE(ds, 'yyyymmdd') AS dt,
                    SUM(activity_gmv) AS gmv,
                    SUM(activity_expense) AS expense
                FROM
                    {get_activity_table(brand)}
                WHERE
                    TO_DATE(ds, 'yyyymmdd') >= TO_DATE('2024-12-31', 'yyyy-mm-dd')
                    AND TO_DATE(ds, 'yyyymmdd') <= CURRENT_DATE
                    AND {filter_condition}
                GROUP BY
                    ds
            ),
            current_data AS (
                SELECT
                    dt,
                    gmv,
                    expense
                FROM
                    base_data
            ),
            prev_day_data AS (
                SELECT
                    dt + INTERVAL '1 day' AS dt,
                    gmv AS gmv_prev_day,
                    expense AS expense_prev_day
                FROM
                    base_data
            )
            SELECT
                TO_CHAR(c.dt, 'YYYYMMDD') AS date,
                c.gmv AS gmv_target,
                pd.gmv_prev_day AS gmv_base,
                (c.gmv - pd.gmv_prev_day) AS gmv_change,
                CASE
                    WHEN pd.gmv_prev_day = 0 OR pd.gmv_prev_day IS NULL THEN NULL
                    ELSE (c.gmv - pd.gmv_prev_day) * 1.0 / pd.gmv_prev_day
                END AS gmv_change_rate,
                c.expense AS expense_target,
                pd.expense_prev_day AS expense_base,
                (c.expense - pd.expense_prev_day) AS expense_change,
                CASE
                    WHEN pd.expense_prev_day = 0 OR pd.expense_prev_day IS NULL THEN NULL
                    ELSE (c.expense - pd.expense_prev_day) * 1.0 / pd.expense_prev_day
                END AS expense_change_rate
            FROM
                current_data c
            LEFT JOIN
                prev_day_data pd
            ON
                c.dt = pd.dt
            ORDER BY
                c.dt
            """
        elif compare_type == "周同比":
            sql = f"""
            WITH base_data AS (
                SELECT
                    TO_DATE(ds, 'yyyymmdd') AS dt,
                    SUM(activity_gmv) AS gmv,
                    SUM(activity_expense) AS expense
                FROM
                    {get_activity_table(brand)} 
                WHERE
                    TO_DATE(ds, 'yyyymmdd') >= TO_DATE('2024-12-25', 'yyyy-mm-dd')
                    AND TO_DATE(ds, 'yyyymmdd') <= CURRENT_DATE
                    AND {filter_condition}
                GROUP BY
                    ds
            ),
            current_data AS (
                SELECT
                    dt,
                    gmv,
                    expense
                FROM
                    base_data
            ),
            last_week_data AS (
                SELECT
                    dt + INTERVAL '7 days' AS dt,
                    gmv AS gmv_last_week,
                    expense AS expense_last_week
                FROM
                    base_data
            )
            SELECT
                TO_CHAR(c.dt, 'YYYYMMDD') AS date,
                c.gmv AS gmv_target,
                lw.gmv_last_week AS gmv_base,
                (c.gmv - lw.gmv_last_week) AS gmv_change,
                CASE 
                    WHEN lw.gmv_last_week = 0 OR lw.gmv_last_week IS NULL THEN NULL
                    ELSE (c.gmv - lw.gmv_last_week) * 1.0 / lw.gmv_last_week
                END AS gmv_change_rate,
                c.expense AS expense_target,
                lw.expense_last_week AS expense_base,
                (c.expense - lw.expense_last_week) AS expense_change,
                CASE 
                    WHEN lw.expense_last_week = 0 OR lw.expense_last_week IS NULL THEN NULL
                    ELSE (c.expense - lw.expense_last_week) * 1.0 / lw.expense_last_week
                END AS expense_change_rate
            FROM
                current_data c
            LEFT JOIN
                last_week_data lw
            ON
                c.dt = lw.dt
            ORDER BY
                c.dt
            """
        elif compare_type == "月同比":
            sql = f"""
            WITH base_data AS (
                SELECT
                    TO_DATE(ds, 'yyyymmdd') AS dt,
                    SUM(activity_gmv) AS gmv,
                    SUM(activity_expense) AS expense
                FROM
                    {get_activity_table(brand)} 
                WHERE
                    TO_DATE(ds, 'yyyymmdd') >= TO_DATE('2024-12-01', 'yyyy-mm-dd')
                    AND TO_DATE(ds, 'yyyymmdd') <= CURRENT_DATE
                    AND {filter_condition}
                GROUP BY
                    ds
            ),
            current_data AS (
                SELECT
                    dt,
                    gmv,
                    expense
                FROM
                    base_data
            ),
            last_month_data AS (
                SELECT
                    dt + INTERVAL '1 month' AS dt,
                    gmv AS gmv_last_month,
                    expense AS expense_last_month
                FROM
                    base_data
            )
            SELECT
                TO_CHAR(c.dt, 'YYYYMMDD') AS date,
                c.gmv AS gmv_target,
                lm.gmv_last_month AS gmv_base,
                (c.gmv - lm.gmv_last_month) AS gmv_change,
                CASE 
                    WHEN lm.gmv_last_month = 0 OR lm.gmv_last_month IS NULL THEN NULL
                    ELSE (c.gmv - lm.gmv_last_month) * 1.0 / lm.gmv_last_month
                END AS gmv_change_rate,
                c.expense AS expense_target,
                lm.expense_last_month AS expense_base,
                (c.expense - lm.expense_last_month) AS expense_change,
                CASE 
                    WHEN lm.expense_last_month = 0 OR lm.expense_last_month IS NULL THEN NULL
                    ELSE (c.expense - lm.expense_last_month) * 1.0 / lm.expense_last_month
                END AS expense_change_rate
            FROM
                current_data c
            LEFT JOIN
                last_month_data lm
            ON
                c.dt = lm.dt
            ORDER BY
                c.dt
            """
        elif compare_type == "年同比":
            sql = f"""
            WITH current_year_data AS (
                SELECT
                    TO_DATE(ds, 'yyyymmdd') AS dt,
                    SUM(activity_gmv) AS gmv,
                    SUM(activity_expense) AS expense
                FROM
                    {get_activity_table(brand)} 
                WHERE
                    ds >= '20250101'
                    AND ds <= TO_CHAR(CURRENT_DATE - INTERVAL '1 day', 'YYYYMMDD')
                    AND {filter_condition}
                GROUP BY
                    ds
            ),
            last_year_data AS (
                SELECT
                    TO_DATE(ds, 'yyyymmdd') AS dt,
                    SUM(activity_gmv) AS gmv,
                    SUM(activity_expense) AS expense
                FROM
                    {get_activity_table(brand)} 
                WHERE
                    ds >= '20240101'
                    AND ds <= TO_CHAR(CURRENT_DATE - INTERVAL '1 day' - INTERVAL '1 year', 'YYYYMMDD')
                    AND {filter_condition}
                GROUP BY
                    ds
            ),
            -- 计算日期映射关系（去年同日）
            date_mapping AS (
                SELECT 
                    dt AS current_dt,
                    dt - INTERVAL '1 year' AS target_last_year_date
                FROM current_year_data
            ),
            -- 匹配去年同日的数据
            matched_data AS (
                SELECT
                    dm.current_dt,
                    ly.gmv AS gmv_last_year,
                    ly.expense AS expense_last_year
                FROM
                    date_mapping dm
                LEFT JOIN
                    last_year_data ly
                ON
                    ly.dt = dm.target_last_year_date
            )
            SELECT
                TO_CHAR(c.dt, 'YYYYMMDD') AS date,
                c.gmv AS gmv_target,
                md.gmv_last_year AS gmv_base,
                (c.gmv - md.gmv_last_year) AS gmv_change,
                CASE 
                    WHEN md.gmv_last_year = 0 OR md.gmv_last_year IS NULL THEN NULL
                    ELSE (c.gmv - md.gmv_last_year) * 1.0 / md.gmv_last_year
                END AS gmv_change_rate,
                c.expense AS expense_target,
                md.expense_last_year AS expense_base,
                (c.expense - md.expense_last_year) AS expense_change,
                CASE 
                    WHEN md.expense_last_year = 0 OR md.expense_last_year IS NULL THEN NULL
                    ELSE (c.expense - md.expense_last_year) * 1.0 / md.expense_last_year
                END AS expense_change_rate
            FROM
                current_year_data c
            LEFT JOIN
                matched_data md
            ON
                c.dt = md.current_dt
            ORDER BY
                c.dt
            """
        elif compare_type == "年同比(按周)" or compare_type == "年同比（按周）":
            sql = f"""
            WITH current_year_data AS (
                SELECT
                    TO_DATE(ds, 'yyyymmdd') AS dt,
                    SUM(activity_gmv) AS gmv,
                    SUM(activity_expense) AS expense
                FROM
                    {get_activity_table(brand)} 
                WHERE
                    ds >= '20250101'
                    AND ds <= TO_CHAR(CURRENT_DATE - INTERVAL '1 day', 'YYYYMMDD')
                    AND {filter_condition}
                GROUP BY
                    ds
            ),
            last_year_data AS (
                SELECT
                    TO_DATE(ds, 'yyyymmdd') AS dt,
                    SUM(activity_gmv) AS gmv,
                    SUM(activity_expense) AS expense
                FROM
                    {get_activity_table(brand)} 
                WHERE
                    ds >= '20240101'
                    AND ds <= TO_CHAR(CURRENT_DATE - INTERVAL '1 day' - INTERVAL '1 year' + INTERVAL '7 days', 'YYYYMMDD')
                    AND {filter_condition}
                GROUP BY
                    ds
            ),
            -- 计算日期映射关系（去年同周同日）
            date_mapping AS (
                SELECT 
                    dt AS current_dt,
                    -- 使用ISO周次计算去年同周同日，处理跨年边界
                    CASE 
                        WHEN EXTRACT(week FROM dt) > 52 THEN
                            -- 处理第53周的情况（跨年）
                            dt - INTERVAL '1 year'
                        ELSE
                            -- 正常情况：使用ISO周次计算
                            (DATE_TRUNC('year', dt - INTERVAL '1 year')::date + 
                             (EXTRACT(week FROM dt)::int - 1) * 7 + 
                             (EXTRACT(isodow FROM dt)::int - 1))
                    END AS target_last_year_date
                FROM current_year_data
            ),
            -- 匹配去年同周同日的数据
            matched_data AS (
                SELECT
                    dm.current_dt,
                    ly.gmv AS gmv_last_year,
                    ly.expense AS expense_last_year
                FROM
                    date_mapping dm
                LEFT JOIN
                    last_year_data ly
                ON
                    ly.dt = dm.target_last_year_date
            )
            SELECT
                TO_CHAR(c.dt, 'YYYYMMDD') AS date,
                c.gmv AS gmv_target,
                md.gmv_last_year AS gmv_base,
                (c.gmv - md.gmv_last_year) AS gmv_change,
                CASE 
                    WHEN md.gmv_last_year = 0 OR md.gmv_last_year IS NULL THEN NULL
                    ELSE (c.gmv - md.gmv_last_year) * 1.0 / md.gmv_last_year
                END AS gmv_change_rate,
                c.expense AS expense_target,
                md.expense_last_year AS expense_base,
                (c.expense - md.expense_last_year) AS expense_change,
                CASE 
                    WHEN md.expense_last_year = 0 OR md.expense_last_year IS NULL THEN NULL
                    ELSE (c.expense - md.expense_last_year) * 1.0 / md.expense_last_year
                END AS expense_change_rate
            FROM
                current_year_data c
            LEFT JOIN
                matched_data md
            ON
                c.dt = md.current_dt
            ORDER BY
                c.dt
            """
        else:  # 默认使用周同比
            sql = f"""
            WITH base_data AS (
                SELECT
                    TO_DATE(ds, 'yyyymmdd') AS dt,
                    SUM(activity_gmv) AS gmv,
                    SUM(activity_expense) AS expense
                FROM
                    {get_activity_table(brand)} 
                WHERE
                    TO_DATE(ds, 'yyyymmdd') >= TO_DATE('2024-12-25', 'yyyy-mm-dd')
                    AND TO_DATE(ds, 'yyyymmdd') <= CURRENT_DATE
                    AND {filter_condition}
                GROUP BY
                    ds
            ),
            current_data AS (
                SELECT
                    dt,
                    gmv,
                    expense
                FROM
                    base_data
            ),
            last_week_data AS (
                SELECT
                    dt + INTERVAL '7 days' AS dt,
                    gmv AS gmv_last_week,
                    expense AS expense_last_week
                FROM
                    base_data
            )
            SELECT
                TO_CHAR(c.dt, 'YYYYMMDD') AS date,
                c.gmv AS gmv_target,
                lw.gmv_last_week AS gmv_base,
                (c.gmv - lw.gmv_last_week) AS gmv_change,
                CASE 
                    WHEN lw.gmv_last_week = 0 OR lw.gmv_last_week IS NULL THEN NULL
                    ELSE (c.gmv - lw.gmv_last_week) * 1.0 / lw.gmv_last_week
                END AS gmv_change_rate,
                c.expense AS expense_target,
                lw.expense_last_week AS expense_base,
                (c.expense - lw.expense_last_week) AS expense_change,
                CASE 
                    WHEN lw.expense_last_week = 0 OR lw.expense_last_week IS NULL THEN NULL
                    ELSE (c.expense - lw.expense_last_week) * 1.0 / lw.expense_last_week
                END AS expense_change_rate
            FROM
                current_data c
            LEFT JOIN
                last_week_data lw
            ON
                c.dt = lw.dt
            ORDER BY
                c.dt
            """
        
        # 执行SQL并获取结果
        # 假设这里有一个execute_sql函数，用于执行SQL并返回DataFrame
        df = get_row_data(sql)
        
        # 处理结果并构建返回列表
        historical_changes = []
        for _, row in df.iterrows():
            if pd.notna(row['gmv_change']) and pd.notna(row['gmv_change_rate']):
                historical_changes.append({
                    "date": row['date'],
                    "gmv_change": round(float(row['gmv_change'])),
                    "gmv_change_rate": float(row['gmv_change_rate']) * 100,
                    "expense_change": round(float(row['expense_change'])) if pd.notna(row['expense_change']) else None,
                    "expense_change_rate": float(row['expense_change_rate']) * 100 if pd.notna(row['expense_change_rate']) else None
                })
        
        return historical_changes
    except Exception as e:
        print(f"获取历史活动GMV波动数据时出错: {str(e)}")
        return []

# 城市（全量GMV）
def get_cities_for_single_date_gmv(tar_date, base_date, brand, sub_brand, province, vender, coupon_mechanism, coupon_threshold, coupon_discount, platform, upc):
    """获取单日日期全量GMV的城市列表"""
    sql = f"""
            select distinct standard_city
            from {get_gmv_table(brand)}
            where (ds = '{tar_date}' or ds = '{base_date}')
            and brand_filter_condition = '{brand}'
            {build_multi_select_condition('sub_brand', sub_brand)}
            {build_multi_select_condition('province', province)}
            {build_multi_select_condition('vender_name', vender)}
            {build_multi_select_condition('platform', platform)}
            {build_multi_select_condition('upc', upc)}
            order by standard_city;
            """
    df = get_row_data(sql)
    return df

def get_cities_for_date_range_gmv(tar_start_date, tar_end_date, base_start_date, base_end_date, brand, sub_brand, province, vender, coupon_mechanism, coupon_threshold, coupon_discount, platform, upc):
    """获取日期范围全量GMV的城市列表"""
    sql = f"""
            select distinct standard_city
            from {get_gmv_table(brand)}
            where (ds between '{tar_start_date}' and '{tar_end_date}' or ds between '{base_start_date}' and '{base_end_date}')
            and brand_filter_condition = '{brand}'
            {build_multi_select_condition('sub_brand', sub_brand)}
            {build_multi_select_condition('province', province)}
            {build_multi_select_condition('vender_name', vender)}
            {build_multi_select_condition('platform', platform)}
            {build_multi_select_condition('upc', upc)}
            order by standard_city;
            """
    df = get_row_data(sql)
    return df

# 城市（活动GMV）
def get_cities_for_single_date_activity(tar_date, base_date, brand, sub_brand, province, vender, coupon_mechanism, coupon_threshold, coupon_discount, platform, upc):
    """获取单日日期活动GMV的城市列表"""
    trim_tar_date = tar_date.replace('-', '')
    trim_base_date = base_date.replace('-', '')
    sql = f"""
            select distinct city
            from {get_activity_table(brand)}
            where (ds = '{trim_tar_date}' or ds = '{trim_base_date}')
            and brand = '{brand}'
            {build_multi_select_condition('sub_brand', sub_brand)}
            {build_multi_select_condition('province', province)}
            {build_multi_select_condition('vender_name', vender)}
            {build_multi_select_condition('coupon_name', coupon_mechanism)}
            {build_multi_select_condition('coupon_threshold', coupon_threshold)}
            {build_multi_select_condition('coupon_discount', coupon_discount)}
            {build_multi_select_condition('platform', platform)}
            {build_multi_select_condition('upc', upc)}
            order by city;
            """
    df = get_row_data(sql)
    return df

def get_cities_for_date_range_activity(tar_start_date, tar_end_date, base_start_date, base_end_date, brand, sub_brand, province, vender, coupon_mechanism, coupon_threshold, coupon_discount, platform, upc):
    """获取日期范围活动GMV的城市列表"""
    trim_tar_start_date = tar_start_date.replace('-', '')
    trim_tar_end_date = tar_end_date.replace('-', '')
    trim_base_start_date = base_start_date.replace('-', '')
    trim_base_end_date = base_end_date.replace('-', '')
    sql = f"""
            select distinct city
            from {get_activity_table(brand)}
            where (ds between '{trim_tar_start_date}' and '{trim_tar_end_date}' or ds between '{trim_base_start_date}' and '{trim_base_end_date}')
            and brand = '{brand}'
            {build_multi_select_condition('sub_brand', sub_brand)}
            {build_multi_select_condition('province', province)}
            {build_multi_select_condition('vender_name', vender)}
            {build_multi_select_condition('coupon_name', coupon_mechanism)}
            {build_multi_select_condition('coupon_threshold', coupon_threshold)}
            {build_multi_select_condition('coupon_discount', coupon_discount)}
            {build_multi_select_condition('platform', platform)}
            {build_multi_select_condition('upc', upc)}
            order by city;
            """
    df = get_row_data(sql)
    return df

def get_drill_down_gmv_result(flag, brand, tar_date, base_date, tar_start_date, tar_end_date, 
                             base_start_date, base_end_date, target_dimension, sub_brand, 
                             province, city, retailer, platform, upc, product_name):
    """
    获取全量GMV的下钻结果
    
    Args:
        flag: 时间类型标识(1:单日期, 2:日期范围)
        brand: 品牌
        tar_date: 目标日期
        base_date: 基准日期
        tar_start_date: 目标开始日期
        tar_end_date: 目标结束日期
        base_start_date: 基准开始日期
        base_end_date: 基准结束日期
        target_dimension: 目标下钻维度
        sub_brand: 子品牌筛选
        province: 省份筛选
        city: 城市筛选
        retailer: 零售商筛选
        platform: 平台筛选
        upc: 商品筛选
    
    Returns:
        list: 下钻结果数据列表
    """
    try:
        # 维度映射到数据库字段
        dimension_field_mapping = {
            '平台': 'platform',
            '省份': 'province', 
            '城市': 'standard_city',
            '零售商': 'vender_name',
            '子品牌': 'sub_brand',
            '商品名称': 'product_name',
            '商品': 'product_name'  # 前端使用"商品"，后端统一映射到product_name
        }
        
        # 获取目标维度对应的数据库字段
        target_field = dimension_field_mapping.get(target_dimension)
        if not target_field:
            print(f"不支持的下钻维度: {target_dimension}")
            return []
        
        # 获取GMV表名
        from db_config import get_gmv_table
        table_name = get_gmv_table(brand)
        print(f"使用的表名: {table_name}")
        
        # 构建筛选条件
        conditions = [f"brand_filter_condition = '{brand}'"]
        
        # 添加基础筛选条件
        if sub_brand != '全部':
            condition = build_multi_select_condition('sub_brand', sub_brand)
            if condition:
                # 只删除开头的"and "，避免影响字段名中的"and"
                cleaned_condition = condition[4:] if condition.startswith('and ') else condition
                conditions.append(cleaned_condition)
        if province != '全部':
            condition = build_multi_select_condition('province', province)
            if condition:
                # 只删除开头的"and "，避免影响字段名中的"and"
                cleaned_condition = condition[4:] if condition.startswith('and ') else condition
                conditions.append(cleaned_condition)
        if city != '全部':
            condition = build_multi_select_condition('standard_city', city)
            if condition:
                # 只删除开头的"and "，避免影响字段名中的"and"
                cleaned_condition = condition[4:] if condition.startswith('and ') else condition
                conditions.append(cleaned_condition)
        if retailer != '全部':
            condition = build_multi_select_condition('vender_name', retailer)
            if condition:
                # 只删除开头的"and "，避免影响字段名中的"and"
                cleaned_condition = condition[4:] if condition.startswith('and ') else condition
                conditions.append(cleaned_condition)
        if platform != '全部':
            condition = build_multi_select_condition('platform', platform)
            if condition:
                # 只删除开头的"and "，避免影响字段名中的"and"
                cleaned_condition = condition[4:] if condition.startswith('and ') else condition
                conditions.append(cleaned_condition)
        if upc != '全部':
            condition = build_multi_select_condition('upc', upc)
            if condition:
                # 只删除开头的"and "，避免影响字段名中的"and"
                cleaned_condition = condition[4:] if condition.startswith('and ') else condition
                conditions.append(cleaned_condition)
        if product_name != '全部':
            condition = build_multi_select_condition('product_name', product_name)
            if condition:
                # 只删除开头的"and "，避免影响字段名中的"and"
                cleaned_condition = condition[4:] if condition.startswith('and ') else condition
                conditions.append(cleaned_condition)
        
        # 确保目标维度字段不为空
        conditions.append(f"{target_field} IS NOT NULL AND {target_field} != '' AND {target_field} != '全部'")
        
        # 根据flag构建不同的查询SQL
        if flag == 1:  # 单日期查询
            # 格式化日期 - GMV表使用YYYY-MM-DD格式
            if tar_date and len(tar_date) == 8 and tar_date.isdigit():
                tar_date_formatted = f"{tar_date[:4]}-{tar_date[4:6]}-{tar_date[6:8]}"
            else:
                tar_date_formatted = tar_date
                
            if base_date and len(base_date) == 8 and base_date.isdigit():
                base_date_formatted = f"{base_date[:4]}-{base_date[4:6]}-{base_date[6:8]}"
            else:
                base_date_formatted = base_date
            
            print(f"单日期查询: tar_date={tar_date_formatted}, base_date={base_date_formatted}")
            
            # 目标日期GMV查询
            if target_dimension in ['商品名称', '商品']:
                # 商品维度需要同时查询product_name和upc字段
                target_sql = f"""
                    SELECT 
                        {target_field} as dimension_value,
                        upc,
                        SUM(gmv) as target_gmv
                    FROM {table_name}
                    WHERE ds = '{tar_date_formatted}' 
                      AND {' AND '.join(conditions)}
                    GROUP BY {target_field}, upc
                    ORDER BY SUM(gmv) DESC
                """
                
                # 基准日期GMV查询
                base_sql = f"""
                    SELECT 
                        {target_field} as dimension_value,
                        upc,
                        SUM(gmv) as base_gmv
                    FROM {table_name}
                    WHERE ds = '{base_date_formatted}' 
                      AND {' AND '.join(conditions)}
                    GROUP BY {target_field}, upc
                """
            else:
                # 其他维度的查询保持不变
                target_sql = f"""
                    SELECT 
                        {target_field} as dimension_value,
                        SUM(gmv) as target_gmv
                    FROM {table_name}
                    WHERE ds = '{tar_date_formatted}' 
                      AND {' AND '.join(conditions)}
                    GROUP BY {target_field}
                    ORDER BY SUM(gmv) DESC
                """
                
                # 基准日期GMV查询
                base_sql = f"""
                    SELECT 
                        {target_field} as dimension_value,
                        SUM(gmv) as base_gmv
                    FROM {table_name}
                    WHERE ds = '{base_date_formatted}' 
                      AND {' AND '.join(conditions)}
                    GROUP BY {target_field}
                """
            
            # 总GMV查询(用于计算贡献度)
            total_target_sql = f"""
                SELECT SUM(gmv) as total_gmv
                FROM {table_name}
                WHERE ds = '{tar_date_formatted}' 
                  AND {' AND '.join(conditions)}
            """
            
        else:  # 日期范围查询
            # 格式化日期范围 - GMV表使用YYYY-MM-DD格式
            if tar_start_date and len(tar_start_date) == 8 and tar_start_date.isdigit():
                tar_start_formatted = f"{tar_start_date[:4]}-{tar_start_date[4:6]}-{tar_start_date[6:8]}"
            else:
                tar_start_formatted = tar_start_date
                
            if tar_end_date and len(tar_end_date) == 8 and tar_end_date.isdigit():
                tar_end_formatted = f"{tar_end_date[:4]}-{tar_end_date[4:6]}-{tar_end_date[6:8]}"
            else:
                tar_end_formatted = tar_end_date
                
            if base_start_date and len(base_start_date) == 8 and base_start_date.isdigit():
                base_start_formatted = f"{base_start_date[:4]}-{base_start_date[4:6]}-{base_start_date[6:8]}"
            else:
                base_start_formatted = base_start_date
                
            if base_end_date and len(base_end_date) == 8 and base_end_date.isdigit():
                base_end_formatted = f"{base_end_date[:4]}-{base_end_date[4:6]}-{base_end_date[6:8]}"
            else:
                base_end_formatted = base_end_date
            
            print(f"日期范围查询: tar_start={tar_start_formatted}, tar_end={tar_end_formatted}")
            print(f"基准范围: base_start={base_start_formatted}, base_end={base_end_formatted}")
            
            # 目标日期范围GMV查询
            if target_dimension in ['商品名称', '商品']:
                # 商品维度需要同时查询product_name和upc字段
                target_sql = f"""
                    SELECT 
                        {target_field} as dimension_value,
                        upc,
                        SUM(gmv) as target_gmv
                    FROM {table_name}
                    WHERE ds BETWEEN '{tar_start_formatted}' AND '{tar_end_formatted}'
                      AND {' AND '.join(conditions)}
                    GROUP BY {target_field}, upc
                    ORDER BY SUM(gmv) DESC
                """
                
                # 基准日期GMV查询
                base_sql = f"""
                    SELECT 
                        {target_field} as dimension_value,
                        upc,
                        SUM(gmv) as base_gmv
                    FROM {table_name}
                    WHERE ds BETWEEN '{base_start_formatted}' AND '{base_end_formatted}'
                      AND {' AND '.join(conditions)}
                    GROUP BY {target_field}, upc
                """
            else:
                # 其他维度的查询保持不变
                target_sql = f"""
                    SELECT 
                        {target_field} as dimension_value,
                        SUM(gmv) as target_gmv
                    FROM {table_name}
                    WHERE ds BETWEEN '{tar_start_formatted}' AND '{tar_end_formatted}'
                      AND {' AND '.join(conditions)}
                    GROUP BY {target_field}
                    ORDER BY SUM(gmv) DESC
                """
                
                # 基准日期范围GMV查询
                base_sql = f"""
                    SELECT 
                        {target_field} as dimension_value,
                        SUM(gmv) as base_gmv
                    FROM {table_name}
                    WHERE ds BETWEEN '{base_start_formatted}' AND '{base_end_formatted}'
                      AND {' AND '.join(conditions)}
                    GROUP BY {target_field}
                """
            
            # 总GMV查询
            total_target_sql = f"""
                SELECT SUM(gmv) as total_gmv
                FROM {table_name}
                WHERE ds BETWEEN '{tar_start_formatted}' AND '{tar_end_formatted}'
                  AND {' AND '.join(conditions)}
            """
        
        # 执行查询
        print(f"执行目标GMV查询: {target_sql}")
        target_df = get_row_data(target_sql)
        if target_df is None:
            print("目标GMV查询返回None，可能SQL有错误")
            return []
        target_results = target_df.to_dict('records')
        print(f"目标GMV查询结果: {len(target_results)} 条记录, 样例: {target_results[:2] if target_results else '无数据'}")
        
        print(f"执行基准GMV查询: {base_sql}")
        base_df = get_row_data(base_sql)
        if base_df is None:
            print("基准GMV查询返回None，可能SQL有错误")
            return []
        base_results = base_df.to_dict('records')
        print(f"基准GMV查询结果: {len(base_results)} 条记录, 样例: {base_results[:2] if base_results else '无数据'}")
        
        print(f"执行总GMV查询: {total_target_sql}")
        total_df = get_row_data(total_target_sql)
        if total_df is None:
            print("总GMV查询返回None，可能SQL有错误")
            return []
        total_results = total_df.to_dict('records')
        print(f"总GMV查询结果: {total_results}")
        
        # 获取总GMV
        total_gmv = total_results[0]['total_gmv'] if total_results and total_results[0]['total_gmv'] else 0
        
        # 合并结果
        result_dict = {}
        
        # 处理目标期间数据
        for row in target_results:
            dim_value = row['dimension_value']
            if target_dimension in ['商品名称', '商品']:
                # 商品维度使用商品名称+UPC作为唯一键
                upc_value = row.get('upc', '')
                key = f"{dim_value}#{upc_value}"  # 使用#分隔符避免冲突
                result_dict[key] = {
                    'dimension_value': dim_value,
                    'upc': upc_value,
                    'target_gmv': row['target_gmv'] or 0,
                    'base_gmv': 0
                }
            else:
                result_dict[dim_value] = {
                    'dimension_value': dim_value,
                    'target_gmv': row['target_gmv'] or 0,
                    'base_gmv': 0
                }
        
        # 处理基准期间数据
        for row in base_results:
            dim_value = row['dimension_value']
            if target_dimension in ['商品名称', '商品']:
                # 商品维度使用商品名称+UPC作为唯一键
                upc_value = row.get('upc', '')
                key = f"{dim_value}#{upc_value}"
                if key in result_dict:
                    result_dict[key]['base_gmv'] = row['base_gmv'] or 0
                else:
                    result_dict[key] = {
                        'dimension_value': dim_value,
                        'upc': upc_value,
                        'target_gmv': 0,
                        'base_gmv': row['base_gmv'] or 0
                    }
            else:
                if dim_value in result_dict:
                    result_dict[dim_value]['base_gmv'] = row['base_gmv'] or 0
                else:
                    result_dict[dim_value] = {
                        'dimension_value': dim_value,
                        'target_gmv': 0,
                        'base_gmv': row['base_gmv'] or 0
                    }
        
        # 计算总体GMV变化值
        total_target_gmv = sum(data['target_gmv'] for data in result_dict.values())
        total_base_gmv = sum(data['base_gmv'] for data in result_dict.values())
        total_gmv_change = total_target_gmv - total_base_gmv
        
        # 计算衍生指标并格式化
        result_list = []
        for data in result_dict.values():
            target_gmv = data['target_gmv']
            base_gmv = data['base_gmv']
            
            # 计算变化值和变化率
            gmv_change = target_gmv - base_gmv
            if base_gmv > 0:
                gmv_change_rate = (gmv_change / base_gmv) * 100
            else:
                gmv_change_rate = 0 if target_gmv == 0 else float('inf')
            
            # 计算占比和贡献度
            target_ratio = (target_gmv / total_gmv * 100) if total_gmv > 0 else 0
            
            # 计算对比期GMV总量和对比期GMV占比
            total_base_gmv_for_ratio = total_base_gmv if total_base_gmv > 0 else 1
            base_ratio = (base_gmv / total_base_gmv_for_ratio * 100) if total_base_gmv_for_ratio > 0 else 0
            
            # 按照get_gmv_contribution_by_dim的逻辑计算贡献度
            if total_gmv_change == 0:
                gmv_contribution = 0
            elif total_gmv_change > 0:
                gmv_contribution = (gmv_change / total_gmv_change * 100) if total_gmv_change != 0 else 0
            else:  # total_gmv_change < 0
                gmv_contribution = (gmv_change / abs(total_gmv_change) * 100) if total_gmv_change != 0 else 0
            
            # 格式化数据 - 按用户要求的字段顺序：维度值、当期GMV、当期GMV占比、对比期GMV、对比期GMV占比、GMV变化值、GMV变化率、GMV贡献度
            if target_dimension in ['商品名称', '商品']:
                # 商品维度包含UPC字段
                formatted_data = {
                    target_dimension: data['dimension_value'],
                    'UPC': data['upc'],
                    '当期GMV': f"{target_gmv:,.0f}",
                    '当期GMV占比': f"{target_ratio:.2f}%",
                    '对比期GMV': f"{base_gmv:,.0f}",
                    '对比期GMV占比': f"{base_ratio:.2f}%",
                    'GMV变化值': f"{gmv_change:,.0f}",
                    'GMV变化率': f"{gmv_change_rate:.2f}%" if gmv_change_rate != float('inf') else "∞",
                    'GMV贡献度': f"{gmv_contribution:+.2f}%"
                }
            else:
                # 其他维度不包含UPC字段
                formatted_data = {
                    target_dimension: data['dimension_value'],
                    '当期GMV': f"{target_gmv:,.0f}",
                    '当期GMV占比': f"{target_ratio:.2f}%",
                    '对比期GMV': f"{base_gmv:,.0f}",
                    '对比期GMV占比': f"{base_ratio:.2f}%",
                    'GMV变化值': f"{gmv_change:,.0f}",
                    'GMV变化率': f"{gmv_change_rate:.2f}%" if gmv_change_rate != float('inf') else "∞",
                    'GMV贡献度': f"{gmv_contribution:+.2f}%"
                }
            
            result_list.append(formatted_data)
        
        # 按当期GMV降序排序
        result_list.sort(key=lambda x: float(x['当期GMV'].replace(',', '')), reverse=True)
        
        print(f"下钻查询完成，返回 {len(result_list)} 条记录")
        print(f"结果样例: {result_list[:2] if result_list else '无数据'}")
        return result_list
        
    except Exception as e:
        print(f"下钻查询出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return []

def get_drill_down_activity_result(flag, brand, tar_date, base_date, tar_start_date, tar_end_date,
                                  base_start_date, base_end_date, target_dimension, sub_brand,
                                  province, city, retailer, platform, upc, coupon_mechanism,
                                  coupon_threshold, coupon_discount):
    """
    获取活动GMV的下钻结果
    
    Args:
        flag: 时间类型标识(1:单日期, 2:日期范围)
        brand: 品牌
        tar_date: 目标日期
        base_date: 基准日期
        tar_start_date: 目标开始日期
        tar_end_date: 目标结束日期
        base_start_date: 基准开始日期
        base_end_date: 基准结束日期
        target_dimension: 目标下钻维度
        sub_brand: 子品牌筛选
        province: 省份筛选
        city: 城市筛选
        retailer: 零售商筛选
        platform: 平台筛选
        upc: 商品筛选
        coupon_mechanism: 券机制筛选
        coupon_threshold: 券门槛筛选
        coupon_discount: 优惠力度筛选
    
    Returns:
        list: 下钻结果数据列表
    """
    try:
        # 维度映射到数据库字段
        dimension_field_mapping = {
            '平台': 'platform',
            '省份': 'province', 
            '城市': 'city',
            '零售商': 'vender_name',
            '子品牌': 'sub_brand',
            '券机制': 'coupon_name',
            '券门槛': 'coupon_threshold',
            '优惠力度': 'coupon_discount'
        }
        
        # 获取目标维度对应的数据库字段
        target_field = dimension_field_mapping.get(target_dimension)
        if not target_field:
            print(f"不支持的活动GMV下钻维度: {target_dimension}")
            return []
        
        # 获取活动表名
        from db_config import get_activity_table
        table_name = get_activity_table(brand)
        print(f"使用的活动表名: {table_name}")
        
        # 构建筛选条件
        conditions = [f"brand = '{brand}'"]
        
        # 添加基础筛选条件
        if sub_brand != '全部':
            condition = build_multi_select_condition('sub_brand', sub_brand)
            if condition:
                # 只删除开头的"and "，避免影响字段名中的"and"
                cleaned_condition = condition[4:] if condition.startswith('and ') else condition
                conditions.append(cleaned_condition)
        if province != '全部':
            condition = build_multi_select_condition('province', province)
            if condition:
                # 只删除开头的"and "，避免影响字段名中的"and"
                cleaned_condition = condition[4:] if condition.startswith('and ') else condition
                conditions.append(cleaned_condition)
        if city != '全部':
            condition = build_multi_select_condition('city', city)
            if condition:
                # 只删除开头的"and "，避免影响字段名中的"and"
                cleaned_condition = condition[4:] if condition.startswith('and ') else condition
                conditions.append(cleaned_condition)
        if retailer != '全部':
            condition = build_multi_select_condition('vender_name', retailer)
            if condition:
                # 只删除开头的"and "，避免影响字段名中的"and"
                cleaned_condition = condition[4:] if condition.startswith('and ') else condition
                conditions.append(cleaned_condition)
        if platform != '全部':
            condition = build_multi_select_condition('platform', platform)
            if condition:
                # 只删除开头的"and "，避免影响字段名中的"and"
                cleaned_condition = condition[4:] if condition.startswith('and ') else condition
                conditions.append(cleaned_condition)
        if upc != '全部':
            condition = build_multi_select_condition('upc', upc)
            if condition:
                # 只删除开头的"and "，避免影响字段名中的"and"
                cleaned_condition = condition[4:] if condition.startswith('and ') else condition
                conditions.append(cleaned_condition)
        if coupon_mechanism != '全部':
            condition = build_multi_select_condition('coupon_name', coupon_mechanism)
            if condition:
                # 只删除开头的"and "，避免影响字段名中的"and"
                cleaned_condition = condition[4:] if condition.startswith('and ') else condition
                conditions.append(cleaned_condition)
        if coupon_threshold != '全部':
            condition = build_multi_select_condition('coupon_threshold', coupon_threshold)
            if condition:
                # 只删除开头的"and "，避免影响字段名中的"and"
                cleaned_condition = condition[4:] if condition.startswith('and ') else condition
                conditions.append(cleaned_condition)
        if coupon_discount != '全部':
            condition = build_multi_select_condition('coupon_discount', coupon_discount)
            if condition:
                # 只删除开头的"and "，避免影响字段名中的"and"
                cleaned_condition = condition[4:] if condition.startswith('and ') else condition
                conditions.append(cleaned_condition)
        
        # 确保目标维度字段不为空
        conditions.append(f"{target_field} IS NOT NULL AND {target_field} != '' AND {target_field} != '全部'")
        
        # 根据flag构建不同的查询SQL
        if flag == 1:  # 单日期查询
            # 格式化日期 - 活动表使用YYYYMMDD格式
            if tar_date and '-' in tar_date:
                tar_date_formatted = tar_date.replace('-', '')
            else:
                tar_date_formatted = tar_date
                
            if base_date and '-' in base_date:
                base_date_formatted = base_date.replace('-', '')
            else:
                base_date_formatted = base_date
            
            print(f"活动表单日期查询: tar_date={tar_date_formatted}, base_date={base_date_formatted}")
            
            # 目标日期活动GMV查询
            target_sql = f"""
                SELECT 
                    {target_field} as dimension_value,
                    SUM(activity_gmv) as target_gmv,
                    SUM(activity_expense) as target_cost
                FROM {table_name}
                WHERE ds = '{tar_date_formatted}' 
                  AND {' AND '.join(conditions)}
                GROUP BY {target_field}
                ORDER BY SUM(activity_gmv) DESC
            """
            
            # 基准日期活动GMV查询
            base_sql = f"""
                SELECT 
                    {target_field} as dimension_value,
                    SUM(activity_gmv) as base_gmv,
                    SUM(activity_expense) as base_cost
                FROM {table_name}
                WHERE ds = '{base_date_formatted}' 
                  AND {' AND '.join(conditions)}
                GROUP BY {target_field}
            """
            
            # 总活动GMV查询 - 使用与下钻数据相同的筛选条件
            total_target_sql = f"""
                SELECT 
                    SUM(activity_gmv) as total_gmv,
                    SUM(activity_expense) as total_cost
                FROM {table_name}
                WHERE ds = '{tar_date_formatted}' 
                  AND {' AND '.join(conditions)}
            """
            
        else:  # 日期范围查询
            # 格式化日期范围 - 活动表使用YYYYMMDD格式
            if tar_start_date and '-' in tar_start_date:
                tar_start_formatted = tar_start_date.replace('-', '')
            else:
                tar_start_formatted = tar_start_date
                
            if tar_end_date and '-' in tar_end_date:
                tar_end_formatted = tar_end_date.replace('-', '')
            else:
                tar_end_formatted = tar_end_date
                
            if base_start_date and '-' in base_start_date:
                base_start_formatted = base_start_date.replace('-', '')
            else:
                base_start_formatted = base_start_date
                
            if base_end_date and '-' in base_end_date:
                base_end_formatted = base_end_date.replace('-', '')
            else:
                base_end_formatted = base_end_date
            
            print(f"活动表日期范围查询: tar_start={tar_start_formatted}, tar_end={tar_end_formatted}")
            
            # 目标日期范围活动GMV查询
            target_sql = f"""
                SELECT 
                    {target_field} as dimension_value,
                    SUM(activity_gmv) as target_gmv,
                    SUM(activity_expense) as target_cost
                FROM {table_name}
                WHERE ds BETWEEN '{tar_start_formatted}' AND '{tar_end_formatted}'
                  AND {' AND '.join(conditions)}
                GROUP BY {target_field}
                ORDER BY SUM(activity_gmv) DESC
            """
            
            # 基准日期范围活动GMV查询
            base_sql = f"""
                SELECT 
                    {target_field} as dimension_value,
                    SUM(activity_gmv) as base_gmv,
                    SUM(activity_expense) as base_cost
                FROM {table_name}
                WHERE ds BETWEEN '{base_start_formatted}' AND '{base_end_formatted}'
                  AND {' AND '.join(conditions)}
                GROUP BY {target_field}
            """
            
            # 总活动GMV查询 - 使用与下钻数据相同的筛选条件
            total_target_sql = f"""
                SELECT 
                    SUM(activity_gmv) as total_gmv,
                    SUM(activity_expense) as total_cost
                FROM {table_name}
                WHERE ds BETWEEN '{tar_start_formatted}' AND '{tar_end_formatted}'
                  AND {' AND '.join(conditions)}
            """
        
        # 执行查询
        print(f"执行目标活动GMV查询: {target_sql}")
        target_df = get_row_data(target_sql)
        if target_df is None:
            print("目标活动GMV查询返回None，可能SQL有错误")
            return []
        target_results = target_df.to_dict('records')
        
        print(f"执行基准活动GMV查询: {base_sql}")
        base_df = get_row_data(base_sql)
        if base_df is None:
            print("基准活动GMV查询返回None，可能SQL有错误")
            return []
        base_results = base_df.to_dict('records')
        
        print(f"执行总活动GMV查询: {total_target_sql}")
        total_df = get_row_data(total_target_sql)
        if total_df is None:
            print("总活动GMV查询返回None，可能SQL有错误")
            return []
        total_results = total_df.to_dict('records')
        
        # 获取总活动GMV和总营销成本
        total_gmv = total_results[0]['total_gmv'] if total_results and total_results[0]['total_gmv'] else 0
        total_cost = total_results[0]['total_cost'] if total_results and total_results[0]['total_cost'] else 0
        
        # 合并结果
        result_dict = {}
        
        # 处理目标期间数据
        for row in target_results:
            dim_value = row['dimension_value']
            result_dict[dim_value] = {
                'dimension_value': dim_value,
                'target_gmv': row['target_gmv'] or 0,
                'target_cost': row['target_cost'] or 0,
                'base_gmv': 0,
                'base_cost': 0
            }
        
        # 处理基准期间数据
        for row in base_results:
            dim_value = row['dimension_value']
            if dim_value in result_dict:
                result_dict[dim_value]['base_gmv'] = row['base_gmv'] or 0
                result_dict[dim_value]['base_cost'] = row['base_cost'] or 0
            else:
                result_dict[dim_value] = {
                    'dimension_value': dim_value,
                    'target_gmv': 0,
                    'target_cost': 0,
                    'base_gmv': row['base_gmv'] or 0,
                    'base_cost': row['base_cost'] or 0
                }
        
        # 计算总体活动GMV变化值
        total_target_gmv = sum(data['target_gmv'] for data in result_dict.values())
        total_base_gmv = sum(data['base_gmv'] for data in result_dict.values())
        total_gmv_change = total_target_gmv - total_base_gmv
        
        total_target_cost = sum(data['target_cost'] for data in result_dict.values())
        total_base_cost = sum(data['base_cost'] for data in result_dict.values())
        total_cost_change = total_target_cost - total_base_cost
        
        # 计算衍生指标并格式化
        result_list = []
        for data in result_dict.values():
            target_gmv = data['target_gmv']
            target_cost = data['target_cost']
            base_gmv = data['base_gmv']
            base_cost = data['base_cost']
            
            # 计算变化值和变化率
            gmv_change = target_gmv - base_gmv
            if base_gmv > 0:
                gmv_change_rate = (gmv_change / base_gmv) * 100
            else:
                gmv_change_rate = 0 if target_gmv == 0 else float('inf')
            
            # 计算活动GMV占比
            target_ratio = (target_gmv / total_gmv * 100) if total_gmv > 0 else 0
            
            # 计算对比期活动GMV占比
            total_base_gmv_for_ratio = total_base_gmv if total_base_gmv > 0 else 1
            base_ratio = (base_gmv / total_base_gmv_for_ratio * 100) if total_base_gmv_for_ratio > 0 else 0
            
            # 计算活动GMV贡献度
            if total_gmv_change == 0:
                gmv_contribution = 0
            elif total_gmv_change > 0:
                gmv_contribution = (gmv_change / total_gmv_change * 100) if total_gmv_change != 0 else 0
            else:  # total_gmv_change < 0
                gmv_contribution = (gmv_change / abs(total_gmv_change) * 100) if total_gmv_change != 0 else 0
            
            # 计算消耗贡献度
            if total_cost_change == 0:
                cost_contribution = 0
            elif total_cost_change > 0:
                cost_contribution = ((target_cost - base_cost) / total_cost_change * 100) if total_cost_change != 0 else 0
            else:  # total_cost_change < 0
                cost_contribution = ((target_cost - base_cost) / abs(total_cost_change) * 100) if total_cost_change != 0 else 0
            
            # 计算ROI
            target_roi = (target_gmv / target_cost) if target_cost > 0 else 0
            base_roi = (base_gmv / base_cost) if base_cost > 0 else 0
            roi_change = target_roi - base_roi
            
            # 格式化数据 - 维度值在第一列，按逻辑分组排列，统一字段名称
            formatted_data = {
                target_dimension: data['dimension_value'],
                '当期GMV': f"{target_gmv:,.0f}",  # 统一字段名称
                '当期GMV占比': f"{target_ratio:.2f}%",
                '对比期GMV': f"{base_gmv:,.0f}",
                '对比期GMV占比': f"{base_ratio:.2f}%",
                'GMV贡献度': f"{gmv_contribution:+.2f}%",  # 统一字段名称
                'GMV变化值': f"{gmv_change:,.0f}",  # 统一字段名称
                'GMV变化率': f"{gmv_change_rate:.2f}%" if gmv_change_rate != float('inf') else "∞",  # 统一字段名称
                'ROI当前值': f"{target_roi:.2f}",
                'ROI变化值': f"{roi_change:.2f}",
                'ROI变化率': f"{(roi_change/base_roi*100):.2f}%" if base_roi > 0 else "∞",
                '消耗当前值': f"{target_cost:,.0f}",
                '消耗变化值': f"{target_cost - base_cost:,.0f}",
                '消耗变化率': f"{((target_cost - base_cost)/base_cost*100):.2f}%" if base_cost > 0 else "∞",
                '消耗贡献度': f"{cost_contribution:+.2f}%"
            }
            
            result_list.append(formatted_data)
        
        # 按目标活动GMV降序排序
        result_list.sort(key=lambda x: float(x['当期GMV'].replace(',', '')), reverse=True)
        
        print(f"活动GMV下钻查询完成，返回 {len(result_list)} 条记录")
        return result_list
        
    except Exception as e:
        print(f"活动GMV下钻查询出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return []

def get_supply_side_table_name(dimensions, from_dimension_attribution=True):
    """
    根据维度组合选择对应的数仓表

    Args:
        dimensions: 维度列表
        from_dimension_attribution: 是否来自维度归因跳转，默认True
                                   True: 从维度归因跳转，根据维度选择表
                                   False: 直接访问指标归因，使用固定表

    Returns:
        str: 对应的数仓表名
    """
    # 直接访问指标归因场景：统一使用品牌-城市表，不考虑维度
    if not from_dimension_attribution:
        print(f"直接访问指标归因场景，忽略维度 {dimensions}，统一使用品牌-城市表")
        return 'dws_mt_brand_city_details_daily_data_attribute_m'

    # 维度归因场景下的指标归因：根据维度动态选择表
    # 如果包含商品名称维度，优先使用 upc 表
    if 'product_name' in dimensions:
        return 'dws_t_mt_sales_by_upc_list_attribute_m'

    # 如果包含零售商维度或子品牌维度，使用渠道表
    if 'vender_name' in dimensions or 'sub_brand' in dimensions:
        return 'dws_mt_channel_city_details_daily_data_attribute_m'

    # 其他情况使用品牌-城市表
    return 'dws_mt_brand_city_details_daily_data_attribute_m'

def get_supply_side_attribution_result(flag, brand, tar_date, base_date, sub_brand, province, city,
                                     vender, analysis_dimensions_list, platform='全部', upc='全部',
                                     from_dimension_attribution=True):
    """
    获取供给向指标归因结果

    Args:
        flag: 时间类型标识(1:单日期, 2:日期范围)
        brand: 品牌
        tar_date: 目标日期或日期范围
        base_date: 基准日期或日期范围
        sub_brand: 子品牌
        province: 省份
        city: 城市
        vender: 零售商
        analysis_dimensions_list: 分析维度列表（用于选择数仓表）
        platform: 平台
        upc: 商品
        from_dimension_attribution: 是否来自维度归因跳转，默认True
                                   True: 从维度归因跳转，根据维度选择表
                                   False: 直接访问指标归因，使用固定表

    Returns:
        dict: 供给向指标归因结果
    """
    # 根据访问路径和分析维度选择合适的数仓表
    table_name = get_supply_side_table_name(analysis_dimensions_list, from_dimension_attribution)

    # 打印调试信息
    access_type = "维度归因跳转" if from_dimension_attribution else "直接访问指标归因"
    print(f"访问类型: {access_type}")
    print(f"根据维度 {analysis_dimensions_list} 选择表: {table_name}")

    # 获取整体的供给向指标数据（不按维度分组）
    df = get_supply_side_overall_result(
        flag, brand, tar_date, base_date, sub_brand, province,
        city, vender, platform, upc, table_name, from_dimension_attribution,
        analysis_dimensions_list
    )
    
    if df is not None and not df.empty:
        # 返回整体汇总结果
        results = {"overall": df}
        print(f"供给向指标查询完成，返回整体汇总数据")
        return results
    else:
        print(f"供给向指标查询无数据")
        return {}

def get_supply_side_contribution_by_dim(flag, dim, brand, tar_date, base_date, sub_brand,
                                       province, city, vender, platform, upc, table_name,
                                       from_dimension_attribution=True, analysis_dimensions_list=None):
    """
    获取单个维度的供给向指标贡献度
    
    Args:
        flag: 时间类型标识
        dim: 维度名称
        brand: 品牌
        tar_date: 目标日期
        base_date: 基准日期
        sub_brand: 子品牌
        province: 省份
        city: 城市
        vender: 零售商
        platform: 平台
        upc: 商品
        table_name: 数仓表名
    
    Returns:
        DataFrame: 供给向指标数据
    """
    try:
        # 构建基础筛选条件
        conditions = []
        
        # 根据表名构建品牌筛选条件
        if 'upc_list' in table_name:
            conditions.append(f"collect_brand = '{brand}'")
            brand_field = 'collect_brand'
        else:
            conditions.append(f"collect_brand = '{brand}'")
            brand_field = 'collect_brand'
        
        # 添加其他筛选条件
        if sub_brand != '全部':
            if 'upc_list' in table_name:
                condition = build_multi_select_condition('standard_sub_brand', sub_brand)
            else:
                condition = build_multi_select_condition('collect_sub_brand', sub_brand)
            if condition:
                clean_condition = condition[4:] if condition.startswith('and ') else condition
                conditions.append(clean_condition)

        if province != '全部':
            if 'upc_list' in table_name:
                condition = build_multi_select_condition('standard_province', province)
            else:
                condition = build_multi_select_condition('standard_province', province)
            if condition:
                clean_condition = condition[4:] if condition.startswith('and ') else condition
                conditions.append(clean_condition)

        if city != '全部':
            if 'upc_list' in table_name:
                condition = build_multi_select_condition('standard_city', city)
            else:
                condition = build_multi_select_condition('standard_city', city)
            if condition:
                clean_condition = condition[4:] if condition.startswith('and ') else condition
                conditions.append(clean_condition)

        if vender != '全部' and 'channel' in table_name:
            condition = build_multi_select_condition('vender_name', vender)
            if condition:
                clean_condition = condition[4:] if condition.startswith('and ') else condition
                conditions.append(clean_condition)

        if platform != '全部':
            condition = build_multi_select_condition('platform', platform)
            if condition:
                clean_condition = condition[4:] if condition.startswith('and ') else condition
                conditions.append(clean_condition)

        if upc != '全部' and 'upc_list' in table_name:
            condition = build_multi_select_condition('upc', upc)
            if condition:
                clean_condition = condition[4:] if condition.startswith('and ') else condition
                conditions.append(clean_condition)
        
        # 构建维度字段映射
        dimension_mapping = {
            'platform': 'platform',
            'province': 'standard_province' if 'upc_list' in table_name else 'standard_province',
            'standard_city': 'standard_city' if 'upc_list' in table_name else 'standard_city',
            'vender_name': 'vender_name',
            'sub_brand': 'standard_sub_brand' if 'upc_list' in table_name else 'collect_sub_brand',
            'product_name': 'product_name'
        }
        
        target_field = dimension_mapping.get(dim, dim)
        
        # 确保目标维度字段不为空
        conditions.append(f"{target_field} IS NOT NULL AND {target_field} != '' AND {target_field} != '全部'")
        
        # 构建日期条件和查询 - 统一处理月维度数据
        # 解析日期范围，支持单月和多月
        if flag == 1:  # 单日期模式，实际传入的是月份范围的起始日期
            tar_start = tar_date
            tar_end = tar_date
            base_start = base_date
            base_end = base_date
        else:  # 日期范围模式
            if '至' in tar_date:
                tar_parts = tar_date.split('至')
                tar_start = tar_parts[0]
                tar_end = tar_parts[1]
            else:
                tar_start = tar_date
                tar_end = tar_date
                
            if '至' in base_date:
                base_parts = base_date.split('至')
                base_start = base_parts[0]
                base_end = base_parts[1]
            else:
                base_start = base_date
                base_end = base_date
        
        # 将YYYYMMDD格式的日期转换为YYYYMM格式（因为数据表中存储的是月份格式）
        def format_date_to_month(date_str):
            if isinstance(date_str, str) and len(date_str) == 8 and date_str.isdigit():
                return date_str[:6]  # 提取前6位YYYYMM
            elif '-' in str(date_str):
                # YYYY-MM-DD 格式转换为 YYYYMM
                return date_str.replace('-', '')[:6]
            else:
                return str(date_str)[:6] if len(str(date_str)) >= 6 else date_str
        
        tar_start_month = format_date_to_month(tar_start)
        tar_end_month = format_date_to_month(tar_end)
        base_start_month = format_date_to_month(base_start)
        base_end_month = format_date_to_month(base_end)
        
        # 构建月份分区列表
        tar_months = get_month_partitions_from_dates(tar_start, tar_end)
        base_months = get_month_partitions_from_dates(base_start, base_end)
        all_months = list(set(tar_months + base_months))
        month_conditions = "', '".join(all_months)
        
        print(f"目标月份: {tar_months}, 基准月份: {base_months}, 所有月份: {all_months}")
        print(f"格式化后的月份: 目标({tar_start_month}-{tar_end_month}), 基准({base_start_month}-{base_end_month})")
        
        # 构建日期字段名 - 供给向指标表使用月份字段，不是日期字段
        if 'upc_list' in table_name:
            date_field = 'ms'  # UPC表使用ms字段存储月份
        else:
            date_field = 'ms'  # 品牌城市表也使用ms字段存储月份
        
        # 根据表名确定正确的字段名
        if 'upc_list' in table_name:
            shops_field = 'active_shops'
            poi_field = 'active_shops'  # upc表中用active_shops作为排序字段
        else:
            shops_field = 'onsale_poi_num'
            poi_field = 'onsale_poi_num'  # 其他表中用onsale_poi_num作为排序字段

        # 构建目标月份和基准月份的IN条件
        target_months_condition = "', '".join(tar_months)
        base_months_condition = "', '".join(base_months)

        # 判断是否使用新逻辑（选择onsale_poi_num最大的记录）
        # 条件：from_dimension_attribution=True 且 analysis_dimensions_list 包含零售商 且 实际使用的是零售商相关表
        use_max_poi_logic = False
        if (from_dimension_attribution and analysis_dimensions_list and 'vender_name' in analysis_dimensions_list
            and 'channel_city' in table_name):  # 只有使用零售商表时才启用此逻辑
            use_max_poi_logic = True
            print(f"维度{dim}检测到零售商维度且使用零售商表，使用{poi_field}最大值选择逻辑")
        else:
            print(f"维度{dim}未使用零售商表或非维度归因跳转，使用平均值计算逻辑")

        if use_max_poi_logic:
            # 使用新逻辑：选择onsale_poi_num最大的记录
            sql = f"""
        WITH target_data AS (
            SELECT
                {target_field} as dimension_value,
                SUM(COALESCE({shops_field}, 0)) as target_shops,
                -- 选择{poi_field}最大的记录的指标值，如果{poi_field}相同则随机选择一个
                FIRST_VALUE(COALESCE(store_avg_on_sales_sku, 0)) OVER (
                    PARTITION BY {target_field}
                    ORDER BY COALESCE({poi_field}, 0) DESC, RANDOM()
                    ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING
                ) as target_avg_sku,
                FIRST_VALUE(COALESCE(sku_avg_sales_rate, 0)) OVER (
                    PARTITION BY {target_field}
                    ORDER BY COALESCE({poi_field}, 0) DESC, RANDOM()
                    ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING
                ) as target_sales_rate,
                FIRST_VALUE(COALESCE(sku_sales_avg_gmv, 0)) OVER (
                    PARTITION BY {target_field}
                    ORDER BY COALESCE({poi_field}, 0) DESC, RANDOM()
                    ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING
                ) as target_avg_gmv
            FROM {table_name}
            WHERE {date_field} IN ('{target_months_condition}')
              AND {' AND '.join(conditions)}
            GROUP BY {target_field}, {poi_field}, store_avg_on_sales_sku, sku_avg_sales_rate, sku_sales_avg_gmv
        ),
        target_data_final AS (
            SELECT
                dimension_value,
                target_shops,
                MAX(target_avg_sku) as target_avg_sku,
                MAX(target_sales_rate) as target_sales_rate,
                MAX(target_avg_gmv) as target_avg_gmv
            FROM target_data
            GROUP BY dimension_value, target_shops
        ),
        base_data AS (
            SELECT
                {target_field} as dimension_value,
                SUM(COALESCE({shops_field}, 0)) as base_shops,
                -- 选择{poi_field}最大的记录的指标值，如果{poi_field}相同则随机选择一个
                FIRST_VALUE(COALESCE(store_avg_on_sales_sku, 0)) OVER (
                    PARTITION BY {target_field}
                    ORDER BY COALESCE({poi_field}, 0) DESC, RANDOM()
                    ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING
                ) as base_avg_sku,
                FIRST_VALUE(COALESCE(sku_avg_sales_rate, 0)) OVER (
                    PARTITION BY {target_field}
                    ORDER BY COALESCE({poi_field}, 0) DESC, RANDOM()
                    ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING
                ) as base_sales_rate,
                FIRST_VALUE(COALESCE(sku_sales_avg_gmv, 0)) OVER (
                    PARTITION BY {target_field}
                    ORDER BY COALESCE({poi_field}, 0) DESC, RANDOM()
                    ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING
                ) as base_avg_gmv
            FROM {table_name}
            WHERE {date_field} IN ('{base_months_condition}')
              AND {' AND '.join(conditions)}
            GROUP BY {target_field}, {poi_field}, store_avg_on_sales_sku, sku_avg_sales_rate, sku_sales_avg_gmv
        ),
        base_data_final AS (
            SELECT
                dimension_value,
                base_shops,
                MAX(base_avg_sku) as base_avg_sku,
                MAX(base_sales_rate) as base_sales_rate,
                MAX(base_avg_gmv) as base_avg_gmv
            FROM base_data
            GROUP BY dimension_value, base_shops
        )
        SELECT 
            COALESCE(t.dimension_value, b.dimension_value) as {dim},
            COALESCE(t.target_shops, 0) as target_shops,
            COALESCE(b.base_shops, 0) as base_shops,
            COALESCE(t.target_avg_sku, 0) as target_avg_sku,
            COALESCE(b.base_avg_sku, 0) as base_avg_sku,
            COALESCE(t.target_sales_rate, 0) as target_sales_rate,
            COALESCE(b.base_sales_rate, 0) as base_sales_rate,
            COALESCE(t.target_avg_gmv, 0) as target_avg_gmv,
            COALESCE(b.base_avg_gmv, 0) as base_avg_gmv,
            -- 计算GMV = 铺货门店数 × 店均在售SKU数 × SKU平均动销率 × 动销SKU平均GMV
            COALESCE(t.target_shops, 0) * COALESCE(t.target_avg_sku, 0) * COALESCE(t.target_sales_rate, 0) * COALESCE(t.target_avg_gmv, 0) as target_total_gmv,
            COALESCE(b.base_shops, 0) * COALESCE(b.base_avg_sku, 0) * COALESCE(b.base_sales_rate, 0) * COALESCE(b.base_avg_gmv, 0) as base_total_gmv
        FROM target_data_final t
        FULL OUTER JOIN base_data_final b ON t.dimension_value = b.dimension_value
        ORDER BY (COALESCE(t.target_shops, 0) * COALESCE(t.target_avg_sku, 0) * COALESCE(t.target_sales_rate, 0) * COALESCE(t.target_avg_gmv, 0)) DESC
        """
        
        print(f"供给向指标SQL查询: {sql}")
        
        # 先验证数据表中是否有数据
        validation_sql = f"""
        SELECT 
            COUNT(*) as total_count,
            MIN({date_field}) as min_date,
            MAX({date_field}) as max_date,
            COUNT(DISTINCT collect_brand) as brand_count
        FROM {table_name}
        WHERE {date_field} IN ('{month_conditions}')
        """
        print(f"数据验证SQL: {validation_sql}")
        try:
            validation_df = get_row_data(validation_sql)
            if validation_df is not None and not validation_df.empty:
                print(f"数据验证结果: {validation_df.iloc[0].to_dict()}")
            else:
                print("数据验证查询返回空结果")
        except Exception as e:
            print(f"数据验证查询失败: {e}")
            
        # 检查特定品牌的数据
        brand_check_sql = f"""
        SELECT COUNT(*) as brand_data_count
        FROM {table_name}
        WHERE {date_field} IN ('{month_conditions}') AND collect_brand = '{brand}'
        """
        print(f"品牌数据检查SQL: {brand_check_sql}")
        try:
            brand_df = get_row_data(brand_check_sql)
            if brand_df is not None and not brand_df.empty:
                print(f"品牌'{brand}'数据count: {brand_df.iloc[0]['brand_data_count']}")
                
            # 检查具体月份的数据分布
            month_check_sql = f"""
            SELECT {date_field}, COUNT(*) as count
            FROM {table_name}
            WHERE {date_field} IN ('{month_conditions}') AND collect_brand = '{brand}'
            GROUP BY {date_field}
            ORDER BY {date_field}
            """
            print(f"月份分布检查SQL: {month_check_sql}")
            month_df = get_row_data(month_check_sql)
            if month_df is not None and not month_df.empty:
                print(f"月份数据分布: {month_df.to_dict('records')}")
        except Exception as e:
            print(f"品牌数据检查失败: {e}")
        
        df = get_row_data(sql)
        
        print(f"供给向指标查询结果: df is None = {df is None}, df.empty = {df.empty if df is not None else 'N/A'}")
        if df is not None:
            print(f"查询结果行数: {len(df)}, 列数: {len(df.columns) if not df.empty else 0}")
            if not df.empty:
                print(f"前3行数据: \n{df.head(3)}")
        
        if df is not None and not df.empty:
            # 计算各指标的变化值和变化率
            df['shops_change'] = df['target_shops'] - df['base_shops']
            df['shops_change_rate'] = df.apply(
                lambda x: (x['shops_change'] / x['base_shops']) if x['base_shops'] > 0 else 0, axis=1
            )
            
            df['avg_sku_change'] = df['target_avg_sku'] - df['base_avg_sku']
            df['avg_sku_change_rate'] = df.apply(
                lambda x: (x['avg_sku_change'] / x['base_avg_sku']) if x['base_avg_sku'] > 0 else 0, axis=1
            )
            
            df['sales_rate_change'] = df['target_sales_rate'] - df['base_sales_rate']
            df['sales_rate_change_rate'] = df.apply(
                lambda x: (x['sales_rate_change'] / x['base_sales_rate']) if x['base_sales_rate'] > 0 else 0, axis=1
            )
            
            df['avg_gmv_change'] = df['target_avg_gmv'] - df['base_avg_gmv']
            df['avg_gmv_change_rate'] = df.apply(
                lambda x: (x['avg_gmv_change'] / x['base_avg_gmv']) if x['base_avg_gmv'] > 0 else 0, axis=1
            )
            
            # 计算总GMV的变化值和变化率
            df['total_gmv_change'] = df['target_total_gmv'] - df['base_total_gmv']
            df['total_gmv_change_rate'] = df.apply(
                lambda x: (x['total_gmv_change'] / x['base_total_gmv']) if x['base_total_gmv'] > 0 else 0, axis=1
            )
            
            # 计算各指标的总体变化值，用于计算贡献度
            total_shops_change = df['shops_change'].sum()
            total_avg_sku_change = df['avg_sku_change'].sum()
            total_sales_rate_change = df['sales_rate_change'].sum()
            total_avg_gmv_change = df['avg_gmv_change'].sum()
            total_gmv_change_sum = df['total_gmv_change'].sum()
            
            # 为每行计算LMDI贡献度
            def calculate_row_lmdi_contribution(row):
                try:
                    # 提取LMDI算法所需的参数
                    # 4因子分解：GMV = 门店数 × 店均SKU × SKU动销率 × 动销SKU平均GMV
                    Y0 = row['base_total_gmv']  # 对比期总GMV
                    Y1 = row['target_total_gmv']  # 当期总GMV
                    A0 = row['base_shops']  # 对比期覆盖门店数
                    A1 = row['target_shops']  # 当期覆盖门店数
                    B0 = row['base_avg_sku']  # 对比期店均在售UPC数
                    B1 = row['target_avg_sku']  # 当期店均在售UPC数
                    C0 = row['base_sales_rate']  # 对比期SKU平均动销率
                    C1 = row['target_sales_rate']  # 当期SKU平均动销率
                    D0 = row['base_avg_gmv']  # 对比期动销SKU平均GMV
                    D1 = row['target_avg_gmv']  # 当期动销SKU平均GMV

                    # 检查参数有效性
                    if all(val > 0 for val in [Y0, Y1, A0, A1, B0, B1, C0, C1, D0, D1]):
                        # 调用LMDI 4因子算法计算贡献度
                        lmdi_result = calculate_lmdi_contribution_4_factors(Y0, Y1, A0, A1, B0, B1, C0, C1, D0, D1)

                        if lmdi_result:
                            return {
                                'shops_contribution': lmdi_result['因子A']['相对贡献度'] * 100,  # 门店数
                                'avg_sku_contribution': lmdi_result['因子B']['相对贡献度'] * 100,  # 店均SKU
                                'sales_rate_contribution': lmdi_result['因子C']['相对贡献度'] * 100,  # 动销率
                                'avg_gmv_contribution': lmdi_result['因子D']['相对贡献度'] * 100,  # 平均GMV
                                'total_gmv_contribution': 100.0
                            }
                except:
                    pass

                # 回退到传统计算方法
                def calculate_contribution(change_value, total_change):
                    if total_change == 0:
                        return 0
                    elif total_change > 0:
                        return (change_value / total_change * 100)
                    else:
                        return (change_value / abs(total_change) * 100)

                return {
                    'shops_contribution': calculate_contribution(row['shops_change'], total_shops_change),
                    'avg_sku_contribution': calculate_contribution(row['avg_sku_change'], total_avg_sku_change),
                    'sales_rate_contribution': calculate_contribution(row['sales_rate_change'], total_sales_rate_change),
                    'avg_gmv_contribution': calculate_contribution(row['avg_gmv_change'], total_avg_gmv_change),
                    'total_gmv_contribution': calculate_contribution(row['total_gmv_change'], total_gmv_change_sum)
                }

            # 应用LMDI计算到每一行
            contribution_results = df.apply(calculate_row_lmdi_contribution, axis=1, result_type='expand')

            # 将结果合并到原DataFrame
            for col in ['shops_contribution', 'avg_sku_contribution', 'sales_rate_contribution',
                       'avg_gmv_contribution', 'total_gmv_contribution']:
                df[col] = contribution_results[col]

            print(f"维度 {dim} 使用LMDI算法计算贡献度完成")
        
        return df
        
    except Exception as e:
        print(f"获取供给向指标数据失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def get_month_partitions(start_date, end_date):
    """
    获取日期范围内的月份分区列表
    
    Args:
        start_date: 开始日期 (YYYY-MM-DD)
        end_date: 结束日期 (YYYY-MM-DD)
    
    Returns:
        list: 月份分区列表 (YYYYMM)
    """
    
    start = datetime.strptime(start_date, '%Y-%m-%d')
    end = datetime.strptime(end_date, '%Y-%m-%d')
    
    months = []
    current = start.replace(day=1)  # 从月初开始
    
    while current <= end:
        months.append(current.strftime('%Y%m'))
        # 移动到下个月
        if current.month == 12:
            current = current.replace(year=current.year + 1, month=1)
        else:
            current = current.replace(month=current.month + 1)
    
    return months

def get_month_partitions_from_dates(start_date, end_date):
    """
    从日期范围获取月份分区列表，支持YYYYMMDD和YYYY-MM-DD格式
    
    Args:
        start_date: 开始日期 (YYYYMMDD 或 YYYY-MM-DD)
        end_date: 结束日期 (YYYYMMDD 或 YYYY-MM-DD)
    
    Returns:
        list: 月份分区列表 (YYYYMM)
    """
    def parse_date(date_str):
        if isinstance(date_str, str):
            if len(date_str) == 8 and date_str.isdigit():
                # YYYYMMDD格式
                return datetime.strptime(date_str, '%Y%m%d')
            elif '-' in date_str and len(date_str) == 10:
                # YYYY-MM-DD格式
                return datetime.strptime(date_str, '%Y-%m-%d')
        # 如果无法解析，返回None
        return None
    
    start = parse_date(start_date)
    end = parse_date(end_date)
    
    if not start or not end:
        print(f"日期解析失败: start_date={start_date}, end_date={end_date}")
        # 如果解析失败，尝试从字符串中提取年月信息
        try:
            if isinstance(start_date, str) and len(start_date) >= 6:
                start_year_month = start_date[:6] if start_date.isdigit() else start_date[:7].replace('-', '')
                if len(start_year_month) == 6:
                    start = datetime.strptime(start_year_month + '01', '%Y%m%d')
            
            if isinstance(end_date, str) and len(end_date) >= 6:
                end_year_month = end_date[:6] if end_date.isdigit() else end_date[:7].replace('-', '')
                if len(end_year_month) == 6:
                    end = datetime.strptime(end_year_month + '01', '%Y%m%d')
        except:
            return []
        
        if not start or not end:
            return []
    
    months = set()  # 使用set避免重复
    current = start.replace(day=1)  # 从月初开始
    end_month = end.replace(day=1)  # 处理到包含结束日期的月份
    
    while current <= end_month:
        months.add(current.strftime('%Y%m'))
        # 移动到下个月
        if current.month == 12:
            current = current.replace(year=current.year + 1, month=1)
        else:
            current = current.replace(month=current.month + 1)
    
    return sorted(list(months))

def get_supply_side_sheets_data(results, column_name_mapping, sheet_name_mapping):
    """
    处理供给向指标数据格式，转换为前端需要的格式
    
    Args:
        results: 供给向指标查询结果（现在是整体汇总数据）
        column_name_mapping: 列名映射
        sheet_name_mapping: 表名映射
    
    Returns:
        dict: 格式化后的数据
    """
    try:
        print(f"开始处理供给向指标整体数据")
        
        # 供给向指标列名映射
        supply_column_mapping = {
            # GMV指标
            'target_total_gmv': '当期GMV',
            'base_total_gmv': '对比期GMV',
            'total_gmv_change': 'GMV变化值',
            'total_gmv_change_rate': 'GMV变化率',
            'total_gmv_contribution': 'GMV贡献度',

            # 铺货门店数指标
            'target_shops': '当期铺货门店数',
            'base_shops': '对比期铺货门店数',
            'shops_change': '铺货门店数变化值',
            'shops_change_rate': '铺货门店数变化率',
            'shops_contribution': '铺货门店数贡献度',

            # 建议铺货数指标
            'target_advi_shops': '当期建议铺货数',
            'base_advi_shops': '对比期建议铺货数',
            'advi_shops_change': '建议铺货数变化值',
            'advi_shops_change_rate': '建议铺货数变化率',
            'advi_shops_contribution': '建议铺货数贡献度',

            # 店铺渗透率指标
            'target_penetration_rate': '当期店铺渗透率',
            'base_penetration_rate': '对比期店铺渗透率',
            'penetration_rate_change': '店铺渗透率变化值',
            'penetration_rate_change_rate': '店铺渗透率变化率',
            'penetration_rate_contribution': '店铺渗透率贡献度',

            # 店均在售UPC数指标
            'target_avg_sku': '当期店均在售UPC数',
            'base_avg_sku': '对比期店均在售UPC数',
            'avg_sku_change': '店均在售UPC数变化值',
            'avg_sku_change_rate': '店均在售UPC数变化率',
            'avg_sku_contribution': '店均在售UPC数贡献度',

            # 在售SKU数指标
            'target_total_sku': '当期在售SKU数',
            'base_total_sku': '对比期在售SKU数',
            'total_sku_change': '在售SKU数变化值',
            'total_sku_change_rate': '在售SKU数变化率',
            'total_sku_contribution': '在售SKU数贡献度',

            # SKU平均动销率指标
            'target_sales_rate': '当期SKU平均动销率',
            'base_sales_rate': '对比期SKU平均动销率',
            'sales_rate_change': 'SKU平均动销率变化值',
            'sales_rate_change_rate': 'SKU平均动销率变化率',
            'sales_rate_contribution': 'SKU平均动销率贡献度',

            # 动销SKU平均GMV指标
            'target_avg_gmv': '当期动销SKU平均GMV',
            'base_avg_gmv': '对比期动销SKU平均GMV',
            'avg_gmv_change': '动销SKU平均GMV变化值',
            'avg_gmv_change_rate': '动销SKU平均GMV变化率',
            'avg_gmv_contribution': '动销SKU平均GMV贡献度'
        }
        
        # 检查是否有整体数据
        if 'overall' not in results or results['overall'] is None or results['overall'].empty:
            print("没有找到整体供给向指标数据")
            return {}
        
        df = results['overall']
        print(f"处理整体供给向指标数据，原始列：{df.columns.tolist()}")
        
        # 应用列名映射
        df_copy = df.copy()
        df_copy.rename(columns=supply_column_mapping, inplace=True)
        
        print(f"重命名后的列：{df_copy.columns.tolist()}")
        
        # 供给向指标列顺序 - 按照7个指标分组
        supply_columns = [
            # GMV指标组
            "当期GMV", "对比期GMV", "GMV变化值", "GMV变化率", "GMV贡献度",

            # 铺货门店数指标组
            "当期铺货门店数", "对比期铺货门店数", "铺货门店数变化值", "铺货门店数变化率", "铺货门店数贡献度",

            # 建议铺货数指标组
            "当期建议铺货数", "对比期建议铺货数", "建议铺货数变化值", "建议铺货数变化率", "建议铺货数贡献度",

            # 店铺渗透率指标组
            "当期店铺渗透率", "对比期店铺渗透率", "店铺渗透率变化值", "店铺渗透率变化率", "店铺渗透率贡献度",

            # 店均在售UPC数指标组
            "当期店均在售UPC数", "对比期店均在售UPC数", "店均在售UPC数变化值", "店均在售UPC数变化率", "店均在售UPC数贡献度",

            # 在售SKU数指标组
            "当期在售SKU数", "对比期在售SKU数", "在售SKU数变化值", "在售SKU数变化率", "在售SKU数贡献度",

            # SKU平均动销率指标组
            "当期SKU平均动销率", "对比期SKU平均动销率", "SKU平均动销率变化值", "SKU平均动销率变化率", "SKU平均动销率贡献度",

            # 动销SKU平均GMV指标组
            "当期动销SKU平均GMV", "对比期动销SKU平均GMV", "动销SKU平均GMV变化值", "动销SKU平均GMV变化率", "动销SKU平均GMV贡献度"
        ]
        
        # 只选择实际存在的列
        existing_columns = [col for col in supply_columns if col in df_copy.columns]
        
        print(f"存在的供给向指标列: {existing_columns}")
        
        if not existing_columns:
            print("警告：没有找到任何有效的供给向指标列")
            return {}
            
        df_copy = df_copy[existing_columns]
        
        # 格式化变化率为百分比
        for col in df_copy.columns:
            if '变化率' in col:
                try:
                    df_copy[col] = df_copy[col].apply(lambda x: f"{x*100:.2f}%" if pd.notna(x) else "0.00%")
                    print(f"成功格式化变化率列: {col}")
                except Exception as e:
                    print(f"格式化变化率列 {col} 时出错: {e}")
        
        # 格式化贡献度为百分比
        for col in df_copy.columns:
            if '贡献度' in col:
                try:
                    df_copy[col] = df_copy[col].apply(lambda x: f"{x:.2f}%" if pd.notna(x) else "0.00%")
                    print(f"成功格式化贡献度列: {col}")
                except Exception as e:
                    print(f"格式化贡献度列 {col} 时出错: {e}")
        
        # 格式化数值字段
        for col in df_copy.columns:
            if '变化率' not in col and '贡献度' not in col:
                if 'GMV' in col:
                    # GMV字段格式化为整数，千分位分隔
                    df_copy[col] = df_copy[col].apply(lambda x: f"{x:,.0f}" if pd.notna(x) else "0")
                elif '门店数' in col:
                    # 门店数格式化为整数
                    df_copy[col] = df_copy[col].apply(lambda x: f"{x:,.0f}" if pd.notna(x) else "0")
                else:
                    # 其他数值字段保留4位小数
                    df_copy[col] = df_copy[col].apply(lambda x: f"{x:.4f}" if pd.notna(x) else "0.0000")
        
        # 填充空值
        df_copy = df_copy.fillna('-')
        
        # 转换为字典格式 - 整体数据只有一行
        data_dict = df_copy.to_dict('records')[0] if not df_copy.empty else {}
        
        # 构建7个指标的结构化数据
        indicators_data = {
            "GMV": {
                "当期值": data_dict.get("当期GMV", "0"),
                "对比期值": data_dict.get("对比期GMV", "0"),
                "变化值": data_dict.get("GMV变化值", "0"),
                "变化率": data_dict.get("GMV变化率", "0.00%"),
                "贡献度": data_dict.get("GMV贡献度", "0.00%")
            },
            "铺货门店数": {
                "当期值": data_dict.get("当期铺货门店数", "0"),
                "对比期值": data_dict.get("对比期铺货门店数", "0"),
                "变化值": data_dict.get("铺货门店数变化值", "0"),
                "变化率": data_dict.get("铺货门店数变化率", "0.00%"),
                "贡献度": data_dict.get("铺货门店数贡献度", "0.00%")
            },
            "建议铺货数": {
                "当期值": data_dict.get("当期建议铺货数", "0"),
                "对比期值": data_dict.get("对比期建议铺货数", "0"),
                "变化值": data_dict.get("建议铺货数变化值", "0"),
                "变化率": data_dict.get("建议铺货数变化率", "0.00%"),
                "贡献度": data_dict.get("建议铺货数贡献度", "0.00%")
            },
            "店铺渗透率": {
                "当期值": data_dict.get("当期店铺渗透率", "0.0000"),
                "对比期值": data_dict.get("对比期店铺渗透率", "0.0000"),
                "变化值": data_dict.get("店铺渗透率变化值", "0.0000"),
                "变化率": data_dict.get("店铺渗透率变化率", "0.00%"),
                "贡献度": data_dict.get("店铺渗透率贡献度", "0.00%")
            },
            "店均在售UPC数": {
                "当期值": data_dict.get("当期店均在售UPC数", "0.0000"),
                "对比期值": data_dict.get("对比期店均在售UPC数", "0.0000"),
                "变化值": data_dict.get("店均在售UPC数变化值", "0.0000"),
                "变化率": data_dict.get("店均在售UPC数变化率", "0.00%"),
                "贡献度": data_dict.get("店均在售UPC数贡献度", "0.00%")
            },
            "在售SKU数": {
                "当期值": data_dict.get("当期在售SKU数", "0"),
                "对比期值": data_dict.get("对比期在售SKU数", "0"),
                "变化值": data_dict.get("在售SKU数变化值", "0"),
                "变化率": data_dict.get("在售SKU数变化率", "0.00%"),
                "贡献度": data_dict.get("在售SKU数贡献度", "0.00%")
            },
            "SKU平均动销率": {
                "当期值": data_dict.get("当期SKU平均动销率", "0.0000"),
                "对比期值": data_dict.get("对比期SKU平均动销率", "0.0000"),
                "变化值": data_dict.get("SKU平均动销率变化值", "0.0000"),
                "变化率": data_dict.get("SKU平均动销率变化率", "0.00%"),
                "贡献度": data_dict.get("SKU平均动销率贡献度", "0.00%")
            },
            "动销SKU平均GMV": {
                "当期值": data_dict.get("当期动销SKU平均GMV", "0.0000"),
                "对比期值": data_dict.get("对比期动销SKU平均GMV", "0.0000"),
                "变化值": data_dict.get("动销SKU平均GMV变化值", "0.0000"),
                "变化率": data_dict.get("动销SKU平均GMV变化率", "0.00%"),
                "贡献度": data_dict.get("动销SKU平均GMV贡献度", "0.00%")
            }
        }

        # 将供给向指标数据转换为DataFrame格式，用于Excel导出
        excel_data = []
        indicator_order = ["GMV", "铺货门店数", "建议铺货数", "店铺渗透率", "店均在售UPC数", "在售SKU数", "SKU平均动销率", "动销SKU平均GMV"]

        for indicator in indicator_order:
            if indicator in indicators_data:
                indicator_data = indicators_data[indicator]
                excel_row = {
                    "指标名称": indicator,
                    "当期值": indicator_data.get("当期值", "0"),
                    "对比期值": indicator_data.get("对比期值", "0"),
                    "变化值": indicator_data.get("变化值", "0"),
                    "变化率": indicator_data.get("变化率", "0.00%"),
                    "贡献度": indicator_data.get("贡献度", "0.00%")
                }
                excel_data.append(excel_row)

        # 创建DataFrame
        supply_df = pd.DataFrame(excel_data) if excel_data else pd.DataFrame()

        print(f"供给向指标整体数据处理完成")
        return {
            "供给向指标归因": indicators_data,
            "excel_dataframe": supply_df  # 新增：用于Excel导出的DataFrame
        }
        
    except Exception as e:
        print(f"处理供给向指标数据时出错: {e}")
        import traceback
        traceback.print_exc()
        return {}

def get_supply_side_overall_result(flag, brand, tar_date, base_date, sub_brand, province, city,
                                  vender, platform, upc, table_name, from_dimension_attribution=True,
                                  analysis_dimensions_list=None):
    """
    获取供给向指标的整体汇总结果（不按维度分组），计算相对于全量数据的贡献度

    Args:
        flag: 时间类型标识
        brand: 品牌
        tar_date: 目标日期
        base_date: 基准日期
        sub_brand: 子品牌
        province: 省份
        city: 城市
        vender: 零售商
        platform: 平台
        upc: 商品
        table_name: 数仓表名

    Returns:
        DataFrame: 供给向指标整体数据
    """
    try:
        # 构建基础筛选条件（只有品牌筛选，用于获取全量数据）
        base_conditions = [f"collect_brand = '{brand}'"]

        # 构建带筛选条件的查询条件
        filtered_conditions = [f"collect_brand = '{brand}'"]

        # 添加筛选条件
        has_filters = False
        if sub_brand != '全部':
            if 'upc_list' in table_name:
                condition = build_multi_select_condition('standard_sub_brand', sub_brand)
            else:
                condition = build_multi_select_condition('collect_sub_brand', sub_brand)
            if condition:
                # 只删除开头的"and "，避免影响字段名中的"and"
                clean_condition = condition[4:] if condition.startswith('and ') else condition
                filtered_conditions.append(clean_condition)
                has_filters = True

        if province != '全部':
            if 'upc_list' in table_name:
                condition = build_multi_select_condition('standard_province', province)
            else:
                condition = build_multi_select_condition('standard_province', province)
            if condition:
                # 只删除开头的"and "，避免影响字段名中的"and"
                clean_condition = condition[4:] if condition.startswith('and ') else condition
                filtered_conditions.append(clean_condition)
                has_filters = True

        if city != '全部':
            if 'upc_list' in table_name:
                condition = build_multi_select_condition('standard_city', city)
            else:
                condition = build_multi_select_condition('standard_city', city)
            if condition:
                # 只删除开头的"and "，避免影响字段名中的"and"
                clean_condition = condition[4:] if condition.startswith('and ') else condition
                filtered_conditions.append(clean_condition)
                has_filters = True

        if vender != '全部':
            # 根据表名确定零售商字段名
            if 'upc_list' in table_name:
                # UPC表使用 vender_name 字段
                condition = build_multi_select_condition('vender_name', vender)
            elif 'channel' in table_name:
                # 渠道表使用 vender_name 字段
                condition = build_multi_select_condition('vender_name', vender)
            else:
                # 品牌-城市表不支持零售商筛选
                condition = None

            if condition:
                # 只删除开头的"and "，避免影响字段名中的"and"
                clean_condition = condition[4:] if condition.startswith('and ') else condition
                filtered_conditions.append(clean_condition)
                has_filters = True

        if platform != '全部':
            condition = build_multi_select_condition('platform', platform)
            if condition:
                # 只删除开头的"and "，避免影响字段名中的"and"
                clean_condition = condition[4:] if condition.startswith('and ') else condition
                filtered_conditions.append(clean_condition)
                has_filters = True

        if upc != '全部' and 'upc_list' in table_name:
            condition = build_multi_select_condition('upc', upc)
            if condition:
                # 只删除开头的"and "，避免影响字段名中的"and"
                clean_condition = condition[4:] if condition.startswith('and ') else condition
                filtered_conditions.append(clean_condition)
                has_filters = True
        
        # 构建日期条件
        if flag == 1:  # 单日期模式
            tar_start = tar_date
            tar_end = tar_date
            base_start = base_date
            base_end = base_date
        else:  # 日期范围模式
            if '至' in tar_date:
                tar_parts = tar_date.split('至')
                tar_start = tar_parts[0]
                tar_end = tar_parts[1]
            else:
                tar_start = tar_date
                tar_end = tar_date
                
            if '至' in base_date:
                base_parts = base_date.split('至')
                base_start = base_parts[0]
                base_end = base_parts[1]
            else:
                base_start = base_date
                base_end = base_date
        
        # 构建月份分区列表
        tar_months = get_month_partitions_from_dates(tar_start, tar_end)
        base_months = get_month_partitions_from_dates(base_start, base_end)
        
        print(f"供给向指标整体查询 - 目标月份: {tar_months}, 基准月份: {base_months}")
        print(f"是否有筛选条件: {has_filters}")
        
        # 根据表名确定正确的字段名
        if 'upc_list' in table_name:
            shops_field = 'active_shops'
            advi_shops_field = 'active_shops'  # UPC表中建议铺货数使用相同字段
        else:
            shops_field = 'onsale_poi_num'
            advi_shops_field = 'onsale_poi_num'  # 其他表中建议铺货数使用相同字段

        # 构建目标月份和基准月份的IN条件
        target_months_condition = "', '".join(tar_months)
        base_months_condition = "', '".join(base_months)

        # 判断是否需要使用新的分组聚合逻辑
        # 当下钻维度没有商品名称但有零售商时，使用新的分组聚合逻辑
        use_group_aggregation_logic = False
        use_row_number_logic = False
        partition_fields = []

        if from_dimension_attribution and analysis_dimensions_list:
            # 根据分析维度和实际使用的表确定使用哪种逻辑
            if ('vender_name' in analysis_dimensions_list and 'channel' in table_name and
                'product_name' not in analysis_dimensions_list):
                use_group_aggregation_logic = True
                use_row_number_logic = False
                print(f"检测到零售商维度且使用零售商表（无商品维度），使用分组聚合逻辑")
            elif 'sub_brand' in analysis_dimensions_list:
                use_row_number_logic = False
                partition_fields = ['standard_city', 'collect_sub_brand']
                print(f"检测到子品牌维度且使用零售商表，使用ROW_NUMBER逻辑")
            elif 'product_name' in analysis_dimensions_list and 'upc_list' in table_name:
                use_row_number_logic = False
                partition_fields = ['standard_city', 'upc']
                print(f"检测到商品维度且使用UPC表，使用ROW_NUMBER逻辑")
            else:
                use_row_number_logic = False
                print(f"使用品牌-城市表，使用传统平均值逻辑")
        else:
            # 直接访问指标归因，使用传统平均值逻辑
            use_row_number_logic = False
            print(f"直接访问指标归因，使用传统平均值逻辑")

        if use_row_number_logic:
            partition_clause = ', '.join(partition_fields)
            print(f"ROW_NUMBER分组字段: {partition_clause}")

        # 根据表名确定计算逻辑
        if 'upc_list' in table_name:
            # UPC表使用不同的计算逻辑
            avg_sku_calc = f"COALESCE(SUM(COALESCE({shops_field}, 0) * COALESCE(store_avg_on_sales_sku, 0)) / NULLIF(SUM(COALESCE({shops_field}, 0)), 0), 0)"
            total_sku_calc = f"SUM(COALESCE({shops_field}, 0) * COALESCE(store_avg_on_sales_sku, 0))"
            sales_rate_calc = f"COALESCE(SUM(COALESCE(store_sales_num, 0)) / NULLIF(SUM(COALESCE({shops_field}, 0) * COALESCE(store_avg_on_sales_sku, 0)), 0), 0)"
            avg_gmv_calc = f"COALESCE(SUM(COALESCE(value_of_sales, 0)) / NULLIF(SUM(COALESCE(store_sales_num, 0)), 0), 0)"
        else:
            # 其他表使用标准计算逻辑
            avg_sku_calc = f"COALESCE(SUM(COALESCE({shops_field}, 0) * COALESCE(poi_onsale_upc_num_avg, 0)) / NULLIF(SUM(COALESCE({shops_field}, 0)), 0), 0)"
            total_sku_calc = f"SUM(COALESCE({shops_field}, 0) * COALESCE(poi_onsale_upc_num_avg, 0))"
            sales_rate_calc = f"COALESCE(SUM(COALESCE(store_sales_num, 0)) / NULLIF(SUM(COALESCE({shops_field}, 0) * COALESCE(poi_onsale_upc_num_avg, 0)), 0), 0)"
            avg_gmv_calc = f"COALESCE(SUM(COALESCE(value_of_sales, 0)) / NULLIF(SUM(COALESCE(store_sales_num, 0)), 0), 0)"

        # 如果有筛选条件，需要同时查询全量数据和筛选后数据以计算贡献度
        if has_filters:
            if use_group_aggregation_logic:
                # 使用新的分组聚合逻辑：先按城市、零售商、子品牌分组聚合，然后对各个指标取最大值
                sql = f"""
                WITH filtered_target_grouped AS (
                    SELECT
                        standard_city,
                        vender_name,
                        
                        MAX(COALESCE({shops_field}, 0)) AS onsale_poi_num,
                        MAX(COALESCE(poi_onsale_upc_num_avg, 0)) AS poi_onsale_upc_num_avg,
                        MAX(COALESCE(store_sales_num, 0)) AS store_sales_num,
                        SUM(COALESCE(value_of_sales, 0)) AS group_value_of_sales
                    FROM {table_name}
                    WHERE ms IN ('{target_months_condition}')
                      AND {' AND '.join(filtered_conditions)}
                    GROUP BY standard_city, vender_name
                ),
                filtered_target_data AS (
                    SELECT
                        SUM(group_value_of_sales) as value_of_sales,
                        SUM(onsale_poi_num) as target_shops,
                        SUM(onsale_poi_num) as target_advi_shops,
                        SUM(onsale_poi_num * poi_onsale_upc_num_avg) / NULLIF(SUM(onsale_poi_num), 0) as target_avg_sku,
                        SUM(onsale_poi_num * poi_onsale_upc_num_avg) as target_total_sku,
                        MAX(store_sales_num) / NULLIF(SUM(onsale_poi_num * poi_onsale_upc_num_avg), 0) as target_sales_rate,
                        SUM(group_value_of_sales) / NULLIF(MAX(store_sales_num), 0) as target_avg_gmv
                    FROM filtered_target_grouped
                ),
                filtered_base_grouped AS (
                    SELECT
                        standard_city,
                        vender_name,
                        
                        MAX(COALESCE({shops_field}, 0)) AS onsale_poi_num,
                        MAX(COALESCE(poi_onsale_upc_num_avg, 0)) AS poi_onsale_upc_num_avg,
                        MAX(COALESCE(store_sales_num, 0)) AS store_sales_num,
                        SUM(COALESCE(value_of_sales, 0)) AS group_value_of_sales
                    FROM {table_name}
                    WHERE ms IN ('{base_months_condition}')
                      AND {' AND '.join(filtered_conditions)}
                    GROUP BY standard_city, vender_name
                ),
                filtered_base_data AS (
                    SELECT
                        SUM(group_value_of_sales) as value_of_sales,
                        SUM(onsale_poi_num) as base_shops,
                        SUM(onsale_poi_num) as base_advi_shops,
                        SUM(onsale_poi_num * poi_onsale_upc_num_avg) / NULLIF(SUM(onsale_poi_num), 0) as base_avg_sku,
                        SUM(onsale_poi_num * poi_onsale_upc_num_avg) as base_total_sku,
                        MAX(store_sales_num) / NULLIF(SUM(onsale_poi_num * poi_onsale_upc_num_avg), 0) as base_sales_rate,
                        SUM(group_value_of_sales) / NULLIF(MAX(store_sales_num), 0) as base_avg_gmv
                    FROM filtered_base_grouped
                ),
                total_target_grouped AS (
                    SELECT
                        standard_city,
                        vender_name,
                        
                        MAX(COALESCE({shops_field}, 0)) AS onsale_poi_num,
                        MAX(COALESCE(poi_onsale_upc_num_avg, 0)) AS poi_onsale_upc_num_avg,
                        MAX(COALESCE(store_sales_num, 0)) AS store_sales_num,
                        SUM(COALESCE(value_of_sales, 0)) AS group_value_of_sales
                    FROM {table_name}
                    WHERE ms IN ('{target_months_condition}')
                      AND {' AND '.join(base_conditions)}
                    GROUP BY standard_city, vender_name
                ),
                total_target_data AS (
                    SELECT
                        SUM(group_value_of_sales) as value_of_sales,
                        SUM(onsale_poi_num) as total_target_shops,
                        SUM(onsale_poi_num) as total_target_advi_shops,
                        SUM(onsale_poi_num * poi_onsale_upc_num_avg) / NULLIF(SUM(onsale_poi_num), 0) as total_target_avg_sku,
                        SUM(onsale_poi_num * poi_onsale_upc_num_avg) as total_target_total_sku,
                        MAX(store_sales_num) / NULLIF(SUM(onsale_poi_num * poi_onsale_upc_num_avg), 0) as total_target_sales_rate,
                        SUM(group_value_of_sales) / NULLIF(MAX(store_sales_num), 0) as total_target_avg_gmv
                    FROM total_target_grouped
                ),
                total_base_grouped AS (
                    SELECT
                        standard_city,
                        vender_name,
                        
                        MAX(COALESCE({shops_field}, 0)) AS onsale_poi_num,
                        MAX(COALESCE(poi_onsale_upc_num_avg, 0)) AS poi_onsale_upc_num_avg,
                        MAX(COALESCE(store_sales_num, 0)) AS store_sales_num,
                        SUM(COALESCE(value_of_sales, 0)) AS group_value_of_sales
                    FROM {table_name}
                    WHERE ms IN ('{base_months_condition}')
                      AND {' AND '.join(base_conditions)}
                    GROUP BY standard_city, vender_name
                ),
                total_base_data AS (
                    SELECT
                        SUM(group_value_of_sales) as value_of_sales,
                        SUM(onsale_poi_num) as total_base_shops,
                        SUM(onsale_poi_num) as total_base_advi_shops,
                        SUM(onsale_poi_num * poi_onsale_upc_num_avg) / NULLIF(SUM(onsale_poi_num), 0) as total_base_avg_sku,
                        SUM(onsale_poi_num * poi_onsale_upc_num_avg) as total_base_total_sku,
                        MAX(store_sales_num) / NULLIF(SUM(onsale_poi_num * poi_onsale_upc_num_avg), 0) as total_base_sales_rate,
                        SUM(group_value_of_sales) / NULLIF(MAX(store_sales_num), 0) as total_base_avg_gmv
                    FROM total_base_grouped
                )
                SELECT
                    COALESCE(ft.target_shops, 0) as target_shops,
                    COALESCE(fb.base_shops, 0) as base_shops,
                    COALESCE(ft.target_shops, 0) as target_advi_shops,
                    COALESCE(fb.base_shops, 0) as base_advi_shops,
                    COALESCE(COALESCE(ft.target_shops, 0) * 1.0 / NULLIF(COALESCE(ft.target_shops, 0), 0), 1.0) as target_penetration_rate,
                    COALESCE(COALESCE(fb.base_shops, 0) * 1.0 / NULLIF(COALESCE(fb.base_shops, 0), 0), 1.0) as base_penetration_rate,
                    COALESCE(ft.target_avg_sku, 0) as target_avg_sku,
                    COALESCE(fb.base_avg_sku, 0) as base_avg_sku,
                    COALESCE(ft.target_total_sku, 0) as target_total_sku,
                    COALESCE(fb.base_total_sku, 0) as base_total_sku,
                    COALESCE(ft.target_sales_rate, 0) as target_sales_rate,
                    COALESCE(fb.base_sales_rate, 0) as base_sales_rate,
                    COALESCE(ft.target_avg_gmv, 0) as target_avg_gmv,
                    COALESCE(fb.base_avg_gmv, 0) as base_avg_gmv,
                    -- 筛选后的GMV = 铺货门店数 × 店均在售UPC数 × SKU平均动销率 × 动销SKU平均GMV
                    COALESCE(ft.target_shops, 0) * COALESCE(ft.target_avg_sku, 0) * COALESCE(ft.target_sales_rate, 0) * COALESCE(ft.target_avg_gmv, 0) as target_total_gmv,
                    COALESCE(fb.base_shops, 0) * COALESCE(fb.base_avg_sku, 0) * COALESCE(fb.base_sales_rate, 0) * COALESCE(fb.base_avg_gmv, 0) as base_total_gmv,
                    -- 全量数据
                    COALESCE(tt.total_target_shops, 0) as total_target_shops,
                    COALESCE(tb.total_base_shops, 0) as total_base_shops,
                    COALESCE(tt.total_target_shops, 0) as total_target_advi_shops,
                    COALESCE(tb.total_base_shops, 0) as total_base_advi_shops,
                    COALESCE(COALESCE(tt.total_target_shops, 0) * 1.0 / NULLIF(COALESCE(tt.total_target_shops, 0), 0), 1.0) as total_target_penetration_rate,
                    COALESCE(COALESCE(tb.total_base_shops, 0) * 1.0 / NULLIF(COALESCE(tb.total_base_shops, 0), 0), 1.0) as total_base_penetration_rate,
                    COALESCE(tt.total_target_avg_sku, 0) as total_target_avg_sku,
                    COALESCE(tb.total_base_avg_sku, 0) as total_base_avg_sku,
                    COALESCE(tt.total_target_total_sku, 0) as total_target_total_sku,
                    COALESCE(tb.total_base_total_sku, 0) as total_base_total_sku,
                    COALESCE(tt.total_target_sales_rate, 0) as total_target_sales_rate,
                    COALESCE(tb.total_base_sales_rate, 0) as total_base_sales_rate,
                    COALESCE(tt.total_target_avg_gmv, 0) as total_target_avg_gmv,
                    COALESCE(tb.total_base_avg_gmv, 0) as total_base_avg_gmv,
                    -- 全量GMV
                    COALESCE(tt.total_target_shops, 0) * COALESCE(tt.total_target_avg_sku, 0) * COALESCE(tt.total_target_sales_rate, 0) * COALESCE(tt.total_target_avg_gmv, 0) as total_target_gmv,
                    COALESCE(tb.total_base_shops, 0) * COALESCE(tb.total_base_avg_sku, 0) * COALESCE(tb.total_base_sales_rate, 0) * COALESCE(tb.total_base_avg_gmv, 0) as total_base_gmv
                FROM filtered_target_data ft
                CROSS JOIN filtered_base_data fb
                CROSS JOIN total_target_data tt
                CROSS JOIN total_base_data tb
                """
            elif use_row_number_logic:
                # 使用ROW_NUMBER()逻辑选择每个分组中onsale_poi_num最大的记录
                sql = f"""
                WITH filtered_target_raw AS (
                    SELECT *,
                           ROW_NUMBER() OVER (
                               PARTITION BY {partition_clause}
                               ORDER BY COALESCE({shops_field}, 0) DESC
                           ) AS rn
                    FROM {table_name}
                    WHERE ms IN ('{target_months_condition}')
                      AND {' AND '.join(filtered_conditions)}
                ),
                filtered_target_data AS (
                    SELECT
                        SUM(COALESCE({shops_field}, 0)) as target_shops,
                        SUM(COALESCE({advi_shops_field}, 0)) as target_advi_shops,
                        {avg_sku_calc} as target_avg_sku,
                        {total_sku_calc} as target_total_sku,
                        {sales_rate_calc} as target_sales_rate,
                        {avg_gmv_calc} as target_avg_gmv
                    FROM filtered_target_raw
                    WHERE rn = 1
                ),
                filtered_base_raw AS (
                SELECT *,
                       ROW_NUMBER() OVER (
                           PARTITION BY {partition_clause}
                           ORDER BY COALESCE({shops_field}, 0) DESC
                       ) AS rn
                FROM {table_name}
                WHERE ms IN ('{base_months_condition}')
                  AND {' AND '.join(filtered_conditions)}
            ),
            filtered_base_data AS (
                SELECT
                    SUM(COALESCE({shops_field}, 0)) as base_shops,
                    SUM(COALESCE({advi_shops_field}, 0)) as base_advi_shops,
                    {avg_sku_calc} as base_avg_sku,
                    {total_sku_calc} as base_total_sku,
                    {sales_rate_calc} as base_sales_rate,
                    {avg_gmv_calc} as base_avg_gmv
                FROM filtered_base_raw
                WHERE rn = 1
            ),
            total_target_raw AS (
                SELECT *,
                       ROW_NUMBER() OVER (
                           PARTITION BY {partition_clause}
                           ORDER BY COALESCE({shops_field}, 0) DESC
                       ) AS rn
                FROM {table_name}
                WHERE ms IN ('{target_months_condition}')
                  AND {' AND '.join(base_conditions)}
            ),
            total_target_data AS (
                SELECT
                    SUM(COALESCE({shops_field}, 0)) as total_target_shops,
                    SUM(COALESCE({advi_shops_field}, 0)) as total_target_advi_shops,
                    {avg_sku_calc} as total_target_avg_sku,
                    {total_sku_calc} as total_target_total_sku,
                    {sales_rate_calc} as total_target_sales_rate,
                    {avg_gmv_calc} as total_target_avg_gmv
                FROM total_target_raw
                WHERE rn = 1
            ),
            total_base_raw AS (
                SELECT *,
                       ROW_NUMBER() OVER (
                           PARTITION BY {partition_clause}
                           ORDER BY COALESCE({shops_field}, 0) DESC
                       ) AS rn
                FROM {table_name}
                WHERE ms IN ('{base_months_condition}')
                  AND {' AND '.join(base_conditions)}
            ),
            total_base_data AS (
                SELECT
                    SUM(COALESCE({shops_field}, 0)) as total_base_shops,
                    SUM(COALESCE({advi_shops_field}, 0)) as total_base_advi_shops,
                    {avg_sku_calc} as total_base_avg_sku,
                    {total_sku_calc} as total_base_total_sku,
                    {sales_rate_calc} as total_base_sales_rate,
                    {avg_gmv_calc} as total_base_avg_gmv
                FROM total_base_raw
                WHERE rn = 1
            )
            SELECT
                COALESCE(ft.target_shops, 0) as target_shops,
                COALESCE(fb.base_shops, 0) as base_shops,
                COALESCE(ft.target_advi_shops, 0) as target_advi_shops,
                COALESCE(fb.base_advi_shops, 0) as base_advi_shops,
                COALESCE(COALESCE(ft.target_shops, 0) * 1.0 / NULLIF(COALESCE(ft.target_advi_shops, 0), 0), 0) as target_penetration_rate,
                COALESCE(COALESCE(fb.base_shops, 0) * 1.0 / NULLIF(COALESCE(fb.base_advi_shops, 0), 0), 0) as base_penetration_rate,
                COALESCE(ft.target_avg_sku, 0) as target_avg_sku,
                COALESCE(fb.base_avg_sku, 0) as base_avg_sku,
                COALESCE(ft.target_total_sku, 0) as target_total_sku,
                COALESCE(fb.base_total_sku, 0) as base_total_sku,
                COALESCE(ft.target_sales_rate, 0) as target_sales_rate,
                COALESCE(fb.base_sales_rate, 0) as base_sales_rate,
                COALESCE(ft.target_avg_gmv, 0) as target_avg_gmv,
                COALESCE(fb.base_avg_gmv, 0) as base_avg_gmv,
                -- 筛选后的GMV = 铺货门店数 × 店均在售UPC数 × SKU平均动销率 × 动销SKU平均GMV
                COALESCE(ft.target_shops, 0) * COALESCE(ft.target_avg_sku, 0) * COALESCE(ft.target_sales_rate, 0) * COALESCE(ft.target_avg_gmv, 0) as target_total_gmv,
                COALESCE(fb.base_shops, 0) * COALESCE(fb.base_avg_sku, 0) * COALESCE(fb.base_sales_rate, 0) * COALESCE(fb.base_avg_gmv, 0) as base_total_gmv,
                -- 全量数据
                COALESCE(tt.total_target_shops, 0) as total_target_shops,
                COALESCE(tb.total_base_shops, 0) as total_base_shops,
                COALESCE(tt.total_target_advi_shops, 0) as total_target_advi_shops,
                COALESCE(tb.total_base_advi_shops, 0) as total_base_advi_shops,
                COALESCE(COALESCE(tt.total_target_shops, 0) * 1.0 / NULLIF(COALESCE(tt.total_target_advi_shops, 0), 0), 0) as total_target_penetration_rate,
                COALESCE(COALESCE(tb.total_base_shops, 0) * 1.0 / NULLIF(COALESCE(tb.total_base_advi_shops, 0), 0), 0) as total_base_penetration_rate,
                COALESCE(tt.total_target_avg_sku, 0) as total_target_avg_sku,
                COALESCE(tb.total_base_avg_sku, 0) as total_base_avg_sku,
                COALESCE(tt.total_target_total_sku, 0) as total_target_total_sku,
                COALESCE(tb.total_base_total_sku, 0) as total_base_total_sku,
                COALESCE(tt.total_target_sales_rate, 0) as total_target_sales_rate,
                COALESCE(tb.total_base_sales_rate, 0) as total_base_sales_rate,
                COALESCE(tt.total_target_avg_gmv, 0) as total_target_avg_gmv,
                COALESCE(tb.total_base_avg_gmv, 0) as total_base_avg_gmv,
                -- 全量GMV
                COALESCE(tt.total_target_shops, 0) * COALESCE(tt.total_target_avg_sku, 0) * COALESCE(tt.total_target_sales_rate, 0) * COALESCE(tt.total_target_avg_gmv, 0) as total_target_gmv,
                COALESCE(tb.total_base_shops, 0) * COALESCE(tb.total_base_avg_sku, 0) * COALESCE(tb.total_base_sales_rate, 0) * COALESCE(tb.total_base_avg_gmv, 0) as total_base_gmv
            FROM filtered_target_data ft
            CROSS JOIN filtered_base_data fb
            CROSS JOIN total_target_data tt
            CROSS JOIN total_base_data tb
            """
            else:
                # 使用传统的平均值逻辑
                sql = f"""
                WITH filtered_target_data AS (
                    SELECT
                        SUM(COALESCE({shops_field}, 0)) as target_shops,
                        SUM(COALESCE({advi_shops_field}, 0)) as target_advi_shops,
                        {avg_sku_calc} as target_avg_sku,
                        {total_sku_calc} as target_total_sku,
                        {sales_rate_calc} as target_sales_rate,
                        {avg_gmv_calc} as target_avg_gmv
                    FROM {table_name}
                    WHERE ms IN ('{target_months_condition}')
                      AND {' AND '.join(filtered_conditions)}
                ),
                filtered_base_data AS (
                    SELECT
                        SUM(COALESCE({shops_field}, 0)) as base_shops,
                        SUM(COALESCE({advi_shops_field}, 0)) as base_advi_shops,
                        {avg_sku_calc} as base_avg_sku,
                        {total_sku_calc} as base_total_sku,
                        {sales_rate_calc} as base_sales_rate,
                        {avg_gmv_calc} as base_avg_gmv
                    FROM {table_name}
                    WHERE ms IN ('{base_months_condition}')
                      AND {' AND '.join(filtered_conditions)}
                ),
                total_target_data AS (
                    SELECT
                        SUM(COALESCE({shops_field}, 0)) as total_target_shops,
                        SUM(COALESCE({advi_shops_field}, 0)) as total_target_advi_shops,
                        {avg_sku_calc} as total_target_avg_sku,
                        {total_sku_calc} as total_target_total_sku,
                        {sales_rate_calc} as total_target_sales_rate,
                        {avg_gmv_calc} as total_target_avg_gmv
                    FROM {table_name}
                    WHERE ms IN ('{target_months_condition}')
                      AND {' AND '.join(base_conditions)}
                ),
                total_base_data AS (
                    SELECT
                        SUM(COALESCE({shops_field}, 0)) as total_base_shops,
                        SUM(COALESCE({advi_shops_field}, 0)) as total_base_advi_shops,
                        {avg_sku_calc} as total_base_avg_sku,
                        {total_sku_calc} as total_base_total_sku,
                        {sales_rate_calc} as total_base_sales_rate,
                        {avg_gmv_calc} as total_base_avg_gmv
                    FROM {table_name}
                    WHERE ms IN ('{base_months_condition}')
                      AND {' AND '.join(base_conditions)}
                )
                SELECT
                    COALESCE(ft.target_shops, 0) as target_shops,
                    COALESCE(fb.base_shops, 0) as base_shops,
                    COALESCE(ft.target_advi_shops, 0) as target_advi_shops,
                    COALESCE(fb.base_advi_shops, 0) as base_advi_shops,
                    COALESCE(COALESCE(ft.target_shops, 0) * 1.0 / NULLIF(COALESCE(ft.target_advi_shops, 0), 0), 0) as target_penetration_rate,
                    COALESCE(COALESCE(fb.base_shops, 0) * 1.0 / NULLIF(COALESCE(fb.base_advi_shops, 0), 0), 0) as base_penetration_rate,
                    COALESCE(ft.target_avg_sku, 0) as target_avg_sku,
                    COALESCE(fb.base_avg_sku, 0) as base_avg_sku,
                    COALESCE(ft.target_total_sku, 0) as target_total_sku,
                    COALESCE(fb.base_total_sku, 0) as base_total_sku,
                    COALESCE(ft.target_sales_rate, 0) as target_sales_rate,
                    COALESCE(fb.base_sales_rate, 0) as base_sales_rate,
                    COALESCE(ft.target_avg_gmv, 0) as target_avg_gmv,
                    COALESCE(fb.base_avg_gmv, 0) as base_avg_gmv,
                    -- 筛选后的GMV = 铺货门店数 × 店均在售SKU数 × SKU平均动销率 × 动销SKU平均GMV
                    COALESCE(ft.target_shops, 0) * COALESCE(ft.target_avg_sku, 0) * COALESCE(ft.target_sales_rate, 0) * COALESCE(ft.target_avg_gmv, 0) as target_total_gmv,
                    COALESCE(fb.base_shops, 0) * COALESCE(fb.base_avg_sku, 0) * COALESCE(fb.base_sales_rate, 0) * COALESCE(fb.base_avg_gmv, 0) as base_total_gmv,
                    -- 全量数据
                    COALESCE(tt.total_target_shops, 0) as total_target_shops,
                    COALESCE(tb.total_base_shops, 0) as total_base_shops,
                    COALESCE(tt.total_target_advi_shops, 0) as total_target_advi_shops,
                    COALESCE(tb.total_base_advi_shops, 0) as total_base_advi_shops,
                    COALESCE(COALESCE(tt.total_target_shops, 0) * 1.0 / NULLIF(COALESCE(tt.total_target_advi_shops, 0), 0), 0) as total_target_penetration_rate,
                    COALESCE(COALESCE(tb.total_base_shops, 0) * 1.0 / NULLIF(COALESCE(tb.total_base_advi_shops, 0), 0), 0) as total_base_penetration_rate,
                    COALESCE(tt.total_target_avg_sku, 0) as total_target_avg_sku,
                    COALESCE(tb.total_base_avg_sku, 0) as total_base_avg_sku,
                    COALESCE(tt.total_target_total_sku, 0) as total_target_total_sku,
                    COALESCE(tb.total_base_total_sku, 0) as total_base_total_sku,
                    COALESCE(tt.total_target_sales_rate, 0) as total_target_sales_rate,
                    COALESCE(tb.total_base_sales_rate, 0) as total_base_sales_rate,
                    COALESCE(tt.total_target_avg_gmv, 0) as total_target_avg_gmv,
                    COALESCE(tb.total_base_avg_gmv, 0) as total_base_avg_gmv,
                    -- 全量GMV
                    COALESCE(tt.total_target_shops, 0) * COALESCE(tt.total_target_avg_sku, 0) * COALESCE(tt.total_target_sales_rate, 0) * COALESCE(tt.total_target_avg_gmv, 0) as total_target_gmv,
                    COALESCE(tb.total_base_shops, 0) * COALESCE(tb.total_base_avg_sku, 0) * COALESCE(tb.total_base_sales_rate, 0) * COALESCE(tb.total_base_avg_gmv, 0) as total_base_gmv
                FROM filtered_target_data ft
                CROSS JOIN filtered_base_data fb
                CROSS JOIN total_target_data tt
                CROSS JOIN total_base_data tb
                """
        else:
            # 没有筛选条件的情况
            if use_group_aggregation_logic:
                # 使用新的分组聚合逻辑：先按城市、零售商、子品牌分组聚合，然后对各个指标取最大值
                sql = f"""
                WITH target_grouped AS (
                    SELECT
                        standard_city,
                        vender_name,
                        
                        MAX(COALESCE({shops_field}, 0)) AS onsale_poi_num,
                        MAX(COALESCE(poi_onsale_upc_num_avg, 0)) AS poi_onsale_upc_num_avg,
                        MAX(COALESCE(store_sales_num, 0)) AS store_sales_num,
                        SUM(COALESCE(value_of_sales, 0)) AS group_value_of_sales
                    FROM {table_name}
                    WHERE ms IN ('{target_months_condition}')
                      AND {' AND '.join(base_conditions)}
                    GROUP BY standard_city, vender_name
                ),
                target_data AS (
                    SELECT
                        SUM(group_value_of_sales) as value_of_sales,
                        SUM(onsale_poi_num) as target_shops,
                        SUM(onsale_poi_num) as target_advi_shops,
                        SUM(onsale_poi_num * poi_onsale_upc_num_avg) / NULLIF(SUM(onsale_poi_num), 0) as target_avg_sku,
                        SUM(onsale_poi_num * poi_onsale_upc_num_avg) as target_total_sku,
                        MAX(store_sales_num) / NULLIF(SUM(onsale_poi_num * poi_onsale_upc_num_avg), 0) as target_sales_rate,
                        SUM(group_value_of_sales) / NULLIF(MAX(store_sales_num), 0) as target_avg_gmv
                    FROM target_grouped
                ),
                base_grouped AS (
                    SELECT
                        standard_city,
                        vender_name,
                        
                        MAX(COALESCE({shops_field}, 0)) AS onsale_poi_num,
                        MAX(COALESCE(poi_onsale_upc_num_avg, 0)) AS poi_onsale_upc_num_avg,
                        MAX(COALESCE(store_sales_num, 0)) AS store_sales_num,
                        SUM(COALESCE(value_of_sales, 0)) AS group_value_of_sales
                    FROM {table_name}
                    WHERE ms IN ('{base_months_condition}')
                      AND {' AND '.join(base_conditions)}
                    GROUP BY standard_city, vender_name
                ),
                base_data AS (
                    SELECT
                        SUM(group_value_of_sales) as value_of_sales,
                        SUM(onsale_poi_num) as base_shops,
                        SUM(onsale_poi_num) as base_advi_shops,
                        SUM(onsale_poi_num * poi_onsale_upc_num_avg) / NULLIF(SUM(onsale_poi_num), 0) as base_avg_sku,
                        SUM(onsale_poi_num * poi_onsale_upc_num_avg) as base_total_sku,
                        MAX(store_sales_num) / NULLIF(SUM(onsale_poi_num * poi_onsale_upc_num_avg), 0) as base_sales_rate,
                        SUM(group_value_of_sales) / NULLIF(MAX(store_sales_num), 0) as base_avg_gmv
                    FROM base_grouped
                )
                SELECT
                    COALESCE(t.target_shops, 0) as target_shops,
                    COALESCE(b.base_shops, 0) as base_shops,
                    COALESCE(t.target_shops, 0) as target_advi_shops,
                    COALESCE(b.base_shops, 0) as base_advi_shops,
                    COALESCE(COALESCE(t.target_shops, 0) * 1.0 / NULLIF(COALESCE(t.target_shops, 0), 0), 1.0) as target_penetration_rate,
                    COALESCE(COALESCE(b.base_shops, 0) * 1.0 / NULLIF(COALESCE(b.base_shops, 0), 0), 1.0) as base_penetration_rate,
                    COALESCE(t.target_avg_sku, 0) as target_avg_sku,
                    COALESCE(b.base_avg_sku, 0) as base_avg_sku,
                    COALESCE(t.target_total_sku, 0) as target_total_sku,
                    COALESCE(b.base_total_sku, 0) as base_total_sku,
                    COALESCE(t.target_sales_rate, 0) as target_sales_rate,
                    COALESCE(b.base_sales_rate, 0) as base_sales_rate,
                    COALESCE(t.target_avg_gmv, 0) as target_avg_gmv,
                    COALESCE(b.base_avg_gmv, 0) as base_avg_gmv,
                    -- 计算GMV = 铺货门店数 × 店均在售UPC数 × SKU平均动销率 × 动销SKU平均GMV
                    COALESCE(t.target_shops, 0) * COALESCE(t.target_avg_sku, 0) * COALESCE(t.target_sales_rate, 0) * COALESCE(t.target_avg_gmv, 0) as target_total_gmv,
                    COALESCE(b.base_shops, 0) * COALESCE(b.base_avg_sku, 0) * COALESCE(b.base_sales_rate, 0) * COALESCE(b.base_avg_gmv, 0) as base_total_gmv,
                    -- 全量数据与筛选数据相同
                    COALESCE(t.target_shops, 0) as total_target_shops,
                    COALESCE(b.base_shops, 0) as total_base_shops,
                    COALESCE(t.target_shops, 0) as total_target_advi_shops,
                    COALESCE(b.base_shops, 0) as total_base_advi_shops,
                    COALESCE(COALESCE(t.target_shops, 0) * 1.0 / NULLIF(COALESCE(t.target_shops, 0), 0), 1.0) as total_target_penetration_rate,
                    COALESCE(COALESCE(b.base_shops, 0) * 1.0 / NULLIF(COALESCE(b.base_shops, 0), 0), 1.0) as total_base_penetration_rate,
                    COALESCE(t.target_avg_sku, 0) as total_target_avg_sku,
                    COALESCE(b.base_avg_sku, 0) as total_base_avg_sku,
                    COALESCE(t.target_total_sku, 0) as total_target_total_sku,
                    COALESCE(b.base_total_sku, 0) as total_base_total_sku,
                    COALESCE(t.target_sales_rate, 0) as total_target_sales_rate,
                    COALESCE(b.base_sales_rate, 0) as total_base_sales_rate,
                    COALESCE(t.target_avg_gmv, 0) as total_target_avg_gmv,
                    COALESCE(b.base_avg_gmv, 0) as total_base_avg_gmv,
                    -- 全量GMV
                    COALESCE(t.target_shops, 0) * COALESCE(t.target_avg_sku, 0) * COALESCE(t.target_sales_rate, 0) * COALESCE(t.target_avg_gmv, 0) as total_target_gmv,
                    COALESCE(b.base_shops, 0) * COALESCE(b.base_avg_sku, 0) * COALESCE(b.base_sales_rate, 0) * COALESCE(b.base_avg_gmv, 0) as total_base_gmv
                FROM target_data t
                CROSS JOIN base_data b
                """
            elif use_row_number_logic:
                # 使用ROW_NUMBER()逻辑
                sql = f"""
                WITH target_raw AS (
                    SELECT *,
                           ROW_NUMBER() OVER (
                               PARTITION BY {partition_clause}
                               ORDER BY COALESCE({shops_field}, 0) DESC
                           ) AS rn
                    FROM {table_name}
                    WHERE ms IN ('{target_months_condition}')
                      AND {' AND '.join(base_conditions)}
                ),
                target_data AS (
                    SELECT
                        SUM(COALESCE({shops_field}, 0)) as target_shops,
                        SUM(COALESCE({advi_shops_field}, 0)) as target_advi_shops,
                        {avg_sku_calc} as target_avg_sku,
                        {total_sku_calc} as target_total_sku,
                        {sales_rate_calc} as target_sales_rate,
                        {avg_gmv_calc} as target_avg_gmv
                    FROM target_raw
                    WHERE rn = 1
                ),
            base_raw AS (
                SELECT *,
                       ROW_NUMBER() OVER (
                           PARTITION BY {partition_clause}
                           ORDER BY COALESCE({shops_field}, 0) DESC
                       ) AS rn
                FROM {table_name}
                WHERE ms IN ('{base_months_condition}')
                  AND {' AND '.join(base_conditions)}
            ),
            base_data AS (
                SELECT
                    SUM(COALESCE({shops_field}, 0)) as base_shops,
                    SUM(COALESCE({advi_shops_field}, 0)) as base_advi_shops,
                    {avg_sku_calc} as base_avg_sku,
                    {total_sku_calc} as base_total_sku,
                    {sales_rate_calc} as base_sales_rate,
                    {avg_gmv_calc} as base_avg_gmv
                FROM base_raw
                WHERE rn = 1
            )
            SELECT
                COALESCE(t.target_shops, 0) as target_shops,
                COALESCE(b.base_shops, 0) as base_shops,
                COALESCE(t.target_advi_shops, 0) as target_advi_shops,
                COALESCE(b.base_advi_shops, 0) as base_advi_shops,
                COALESCE(COALESCE(t.target_shops, 0) * 1.0 / NULLIF(COALESCE(t.target_advi_shops, 0), 0), 0) as target_penetration_rate,
                COALESCE(COALESCE(b.base_shops, 0) * 1.0 / NULLIF(COALESCE(b.base_advi_shops, 0), 0), 0) as base_penetration_rate,
                COALESCE(t.target_avg_sku, 0) as target_avg_sku,
                COALESCE(b.base_avg_sku, 0) as base_avg_sku,
                COALESCE(t.target_total_sku, 0) as target_total_sku,
                COALESCE(b.base_total_sku, 0) as base_total_sku,
                COALESCE(t.target_sales_rate, 0) as target_sales_rate,
                COALESCE(b.base_sales_rate, 0) as base_sales_rate,
                COALESCE(t.target_avg_gmv, 0) as target_avg_gmv,
                COALESCE(b.base_avg_gmv, 0) as base_avg_gmv,
                -- 计算GMV = 铺货门店数 × 店均在售UPC数 × SKU平均动销率 × 动销SKU平均GMV
                COALESCE(t.target_shops, 0) * COALESCE(t.target_avg_sku, 0) * COALESCE(t.target_sales_rate, 0) * COALESCE(t.target_avg_gmv, 0) as target_total_gmv,
                COALESCE(b.base_shops, 0) * COALESCE(b.base_avg_sku, 0) * COALESCE(b.base_sales_rate, 0) * COALESCE(b.base_avg_gmv, 0) as base_total_gmv,
                -- 全量数据与筛选数据相同
                COALESCE(t.target_shops, 0) as total_target_shops,
                COALESCE(b.base_shops, 0) as total_base_shops,
                COALESCE(t.target_advi_shops, 0) as total_target_advi_shops,
                COALESCE(b.base_advi_shops, 0) as total_base_advi_shops,
                COALESCE(COALESCE(t.target_shops, 0) * 1.0 / NULLIF(COALESCE(t.target_advi_shops, 0), 0), 0) as total_target_penetration_rate,
                COALESCE(COALESCE(b.base_shops, 0) * 1.0 / NULLIF(COALESCE(b.base_advi_shops, 0), 0), 0) as total_base_penetration_rate,
                COALESCE(t.target_avg_sku, 0) as total_target_avg_sku,
                COALESCE(b.base_avg_sku, 0) as total_base_avg_sku,
                COALESCE(t.target_total_sku, 0) as total_target_total_sku,
                COALESCE(b.base_total_sku, 0) as total_base_total_sku,
                COALESCE(t.target_sales_rate, 0) as total_target_sales_rate,
                COALESCE(b.base_sales_rate, 0) as total_base_sales_rate,
                COALESCE(t.target_avg_gmv, 0) as total_target_avg_gmv,
                COALESCE(b.base_avg_gmv, 0) as total_base_avg_gmv,
                COALESCE(t.target_shops, 0) * COALESCE(t.target_avg_sku, 0) * COALESCE(t.target_sales_rate, 0) * COALESCE(t.target_avg_gmv, 0) as total_target_gmv,
                COALESCE(b.base_shops, 0) * COALESCE(b.base_avg_sku, 0) * COALESCE(b.base_sales_rate, 0) * COALESCE(b.base_avg_gmv, 0) as total_base_gmv
            FROM target_data t
            CROSS JOIN base_data b
            """
            else:
                # 使用传统的平均值逻辑
                sql = f"""
                WITH target_data AS (
                    SELECT
                        SUM(COALESCE({shops_field}, 0)) as target_shops,
                        SUM(COALESCE({advi_shops_field}, 0)) as target_advi_shops,
                        {avg_sku_calc} as target_avg_sku,
                        {total_sku_calc} as target_total_sku,
                        {sales_rate_calc} as target_sales_rate,
                        {avg_gmv_calc} as target_avg_gmv
                    FROM {table_name}
                    WHERE ms IN ('{target_months_condition}')
                      AND {' AND '.join(base_conditions)}
                ),
                base_data AS (
                    SELECT
                        SUM(COALESCE({shops_field}, 0)) as base_shops,
                        SUM(COALESCE({advi_shops_field}, 0)) as base_advi_shops,
                        {avg_sku_calc} as base_avg_sku,
                        {total_sku_calc} as base_total_sku,
                        {sales_rate_calc} as base_sales_rate,
                        {avg_gmv_calc} as base_avg_gmv
                    FROM {table_name}
                    WHERE ms IN ('{base_months_condition}')
                      AND {' AND '.join(base_conditions)}
                )
                SELECT
                    COALESCE(t.target_shops, 0) as target_shops,
                    COALESCE(b.base_shops, 0) as base_shops,
                    COALESCE(t.target_advi_shops, 0) as target_advi_shops,
                    COALESCE(b.base_advi_shops, 0) as base_advi_shops,
                    COALESCE(COALESCE(t.target_shops, 0) * 1.0 / NULLIF(COALESCE(t.target_advi_shops, 0), 0), 0) as target_penetration_rate,
                    COALESCE(COALESCE(b.base_shops, 0) * 1.0 / NULLIF(COALESCE(b.base_advi_shops, 0), 0), 0) as base_penetration_rate,
                    COALESCE(t.target_avg_sku, 0) as target_avg_sku,
                    COALESCE(b.base_avg_sku, 0) as base_avg_sku,
                    COALESCE(t.target_total_sku, 0) as target_total_sku,
                    COALESCE(b.base_total_sku, 0) as base_total_sku,
                    COALESCE(t.target_sales_rate, 0) as target_sales_rate,
                    COALESCE(b.base_sales_rate, 0) as base_sales_rate,
                    COALESCE(t.target_avg_gmv, 0) as target_avg_gmv,
                    COALESCE(b.base_avg_gmv, 0) as base_avg_gmv,
                    -- 计算GMV = 铺货门店数 × 店均在售SKU数 × SKU平均动销率 × 动销SKU平均GMV
                    COALESCE(t.target_shops, 0) * COALESCE(t.target_avg_sku, 0) * COALESCE(t.target_sales_rate, 0) * COALESCE(t.target_avg_gmv, 0) as target_total_gmv,
                    COALESCE(b.base_shops, 0) * COALESCE(b.base_avg_sku, 0) * COALESCE(b.base_sales_rate, 0) * COALESCE(b.base_avg_gmv, 0) as base_total_gmv,
                    -- 全量数据与筛选数据相同
                    COALESCE(t.target_shops, 0) as total_target_shops,
                    COALESCE(b.base_shops, 0) as total_base_shops,
                    COALESCE(t.target_advi_shops, 0) as total_target_advi_shops,
                    COALESCE(b.base_advi_shops, 0) as total_base_advi_shops,
                    COALESCE(COALESCE(t.target_shops, 0) * 1.0 / NULLIF(COALESCE(t.target_advi_shops, 0), 0), 0) as total_target_penetration_rate,
                    COALESCE(COALESCE(b.base_shops, 0) * 1.0 / NULLIF(COALESCE(b.base_advi_shops, 0), 0), 0) as total_base_penetration_rate,
                    COALESCE(t.target_avg_sku, 0) as total_target_avg_sku,
                    COALESCE(b.base_avg_sku, 0) as total_base_avg_sku,
                    COALESCE(t.target_total_sku, 0) as total_target_total_sku,
                    COALESCE(b.base_total_sku, 0) as total_base_total_sku,
                    COALESCE(t.target_sales_rate, 0) as total_target_sales_rate,
                    COALESCE(b.base_sales_rate, 0) as total_base_sales_rate,
                    COALESCE(t.target_avg_gmv, 0) as total_target_avg_gmv,
                    COALESCE(b.base_avg_gmv, 0) as total_base_avg_gmv,
                    COALESCE(t.target_shops, 0) * COALESCE(t.target_avg_sku, 0) * COALESCE(t.target_sales_rate, 0) * COALESCE(t.target_avg_gmv, 0) as total_target_gmv,
                    COALESCE(b.base_shops, 0) * COALESCE(b.base_avg_sku, 0) * COALESCE(b.base_sales_rate, 0) * COALESCE(b.base_avg_gmv, 0) as total_base_gmv
                FROM target_data t
                CROSS JOIN base_data b
                """
        
        print(f"供给向指标整体查询SQL: {sql}")
        
        df = get_row_data(sql)
        
        print(f"供给向指标整体查询结果: df is None = {df is None}, df.empty = {df.empty if df is not None else 'N/A'}")
        if df is not None:
            print(f"查询结果行数: {len(df)}, 列数: {len(df.columns) if not df.empty else 0}")
            if not df.empty:
                print(f"整体数据列名: {df.columns.tolist()}")
                print(f"整体数据: \n{df.iloc[0].to_dict()}")
                # 特别检查SKU平均动销率和动销SKU平均GMV字段
                if 'target_sales_rate' in df.columns:
                    print(f"target_sales_rate值: {df.iloc[0]['target_sales_rate']}")
                if 'base_sales_rate' in df.columns:
                    print(f"base_sales_rate值: {df.iloc[0]['base_sales_rate']}")
                if 'target_avg_gmv' in df.columns:
                    print(f"target_avg_gmv值: {df.iloc[0]['target_avg_gmv']}")
                if 'base_avg_gmv' in df.columns:
                    print(f"base_avg_gmv值: {df.iloc[0]['base_avg_gmv']}")
            else:
                print("供给向指标查询返回空结果")
        
        if df is not None and not df.empty:
            # 计算各指标的变化值和变化率
            df['shops_change'] = df['target_shops'] - df['base_shops']
            df['shops_change_rate'] = df.apply(
                lambda x: (x['shops_change'] / x['base_shops']) if pd.notna(x['base_shops']) and x['base_shops'] > 0 else 0, axis=1
            )

            # 建议铺货数变化
            df['advi_shops_change'] = df['target_advi_shops'] - df['base_advi_shops']
            df['advi_shops_change_rate'] = df.apply(
                lambda x: (x['advi_shops_change'] / x['base_advi_shops']) if pd.notna(x['base_advi_shops']) and x['base_advi_shops'] > 0 else 0, axis=1
            )

            # 店铺渗透率变化
            df['penetration_rate_change'] = df['target_penetration_rate'] - df['base_penetration_rate']
            df['penetration_rate_change_rate'] = df.apply(
                lambda x: (x['penetration_rate_change'] / x['base_penetration_rate']) if pd.notna(x['base_penetration_rate']) and x['base_penetration_rate'] > 0 else 0, axis=1
            )

            df['avg_sku_change'] = df['target_avg_sku'] - df['base_avg_sku']
            df['avg_sku_change_rate'] = df.apply(
                lambda x: (x['avg_sku_change'] / x['base_avg_sku']) if pd.notna(x['base_avg_sku']) and x['base_avg_sku'] > 0 else 0, axis=1
            )

            # 在售SKU数变化
            df['total_sku_change'] = df['target_total_sku'] - df['base_total_sku']
            df['total_sku_change_rate'] = df.apply(
                lambda x: (x['total_sku_change'] / x['base_total_sku']) if pd.notna(x['base_total_sku']) and x['base_total_sku'] > 0 else 0, axis=1
            )

            df['sales_rate_change'] = df['target_sales_rate'] - df['base_sales_rate']
            df['sales_rate_change_rate'] = df.apply(
                lambda x: (x['sales_rate_change'] / x['base_sales_rate']) if pd.notna(x['base_sales_rate']) and x['base_sales_rate'] > 0 else 0, axis=1
            )

            df['avg_gmv_change'] = df['target_avg_gmv'] - df['base_avg_gmv']
            df['avg_gmv_change_rate'] = df.apply(
                lambda x: (x['avg_gmv_change'] / x['base_avg_gmv']) if pd.notna(x['base_avg_gmv']) and x['base_avg_gmv'] > 0 else 0, axis=1
            )

            # 计算总GMV的变化值和变化率
            df['total_gmv_change'] = df['target_total_gmv'] - df['base_total_gmv']
            df['total_gmv_change_rate'] = df.apply(
                lambda x: (x['total_gmv_change'] / x['base_total_gmv']) if pd.notna(x['base_total_gmv']) and x['base_total_gmv'] > 0 else 0, axis=1
            )
            
            # 计算全量数据的变化值（用于计算贡献度）
            df['total_shops_change'] = df['total_target_shops'] - df['total_base_shops']
            df['total_avg_sku_change'] = df['total_target_avg_sku'] - df['total_base_avg_sku']
            df['total_sales_rate_change'] = df['total_target_sales_rate'] - df['total_base_sales_rate']
            df['total_avg_gmv_change'] = df['total_target_avg_gmv'] - df['total_base_avg_gmv']
            df['total_total_gmv_change'] = df['total_target_gmv'] - df['total_base_gmv']
            
            
            row = df.iloc[0]  

            # 提取LMDI算法所需的参数
            # 供给向指标的4因子分解：GMV = 门店数 × 店均SKU × SKU动销率 × 动销SKU平均GMV
            Y0 = row['base_total_gmv']  # 对比期总GMV
            Y1 = row['target_total_gmv']  # 当期总GMV
            A0 = row['base_shops']  # 对比期覆盖门店数
            A1 = row['target_shops']  # 当期覆盖门店数
            B0 = row['base_avg_sku']  # 对比期店均在售UPC数
            B1 = row['target_avg_sku']  # 当期店均在售UPC数
            C0 = row['base_sales_rate']  # 对比期SKU平均动销率
            C1 = row['target_sales_rate']  # 当期SKU平均动销率
            D0 = row['base_avg_gmv']  # 对比期动销SKU平均GMV
            D1 = row['target_avg_gmv']  # 当期动销SKU平均GMV

            print(f"LMDI 4因子参数: Y0={Y0}, Y1={Y1}, A0={A0}, A1={A1}, B0={B0}, B1={B1}, C0={C0}, C1={C1}, D0={D0}, D1={D1}")

            # 检查参数有效性
            if all(pd.notna(val) and val > 0 for val in [Y0, Y1, A0, A1, B0, B1, C0, C1, D0, D1]):
                # 调用LMDI 4因子算法计算贡献度
                lmdi_result = calculate_lmdi_contribution_4_factors(Y0, Y1, A0, A1, B0, B1, C0, C1, D0, D1)

                if lmdi_result:
                    print("使用LMDI算法计算贡献度")
                    # 将LMDI 4因子结果映射到DataFrame列
                    df['shops_contribution'] = lmdi_result['因子A']['相对贡献度'] * 100  # 门店数
                    df['avg_sku_contribution'] = lmdi_result['因子B']['相对贡献度'] * 100  # 店均SKU
                    df['sales_rate_contribution'] = lmdi_result['因子C']['相对贡献度'] * 100  # 动销率
                    df['avg_gmv_contribution'] = lmdi_result['因子D']['相对贡献度'] * 100  # 平均GMV
                    df['total_gmv_contribution'] = 100.0  # 总GMV贡献度为100%

                    # 打印LMDI贡献度结果
                    print("LMDI 4因子贡献度计算结果:")
                    total_contrib = 0
                    factor_names = ['门店数', '店均SKU', '动销率', '平均GMV']
                    for factor_key, factor_name in zip(['因子A', '因子B', '因子C', '因子D'], factor_names):
                        contrib = lmdi_result[factor_key]['相对贡献度']*100
                        total_contrib += contrib
                        print(f"  {factor_name}: {contrib:.2f}%")
                    print(f"  总和验证: {total_contrib:.2f}%")
                else:
                    print("LMDI算法计算失败，使用传统方法")
                    # 回退到传统计算方法
                    def calculate_contribution(filtered_change, total_change):
                        if total_change == 0:
                            return 100.0 if not has_filters else 0.0
                        elif total_change > 0:
                            return (filtered_change / total_change * 100)
                        else:
                            return (filtered_change / abs(total_change) * 100)

                    df['shops_contribution'] = df.apply(
                        lambda x: calculate_contribution(x['shops_change'], x['total_shops_change']), axis=1
                    )
                    df['avg_sku_contribution'] = df.apply(
                        lambda x: calculate_contribution(x['avg_sku_change'], x['total_avg_sku_change']), axis=1
                    )
                    df['sales_rate_contribution'] = df.apply(
                        lambda x: calculate_contribution(x['sales_rate_change'], x['total_sales_rate_change']), axis=1
                    )
                    df['avg_gmv_contribution'] = df.apply(
                        lambda x: calculate_contribution(x['avg_gmv_change'], x['total_avg_gmv_change']), axis=1
                    )
                    df['total_gmv_contribution'] = df.apply(
                        lambda x: calculate_contribution(x['total_gmv_change'], x['total_total_gmv_change']), axis=1
                    )
            else:
                print("参数包含零值或负值，使用传统方法计算贡献度")
                # 回退到传统计算方法
                def calculate_contribution(filtered_change, total_change):
                    if total_change == 0:
                        return 100.0 if not has_filters else 0.0
                    elif total_change > 0:
                        return (filtered_change / total_change * 100)
                    else:
                        return (filtered_change / abs(total_change) * 100)

                df['shops_contribution'] = df.apply(
                    lambda x: calculate_contribution(x['shops_change'], x['total_shops_change']), axis=1
                )
                df['avg_sku_contribution'] = df.apply(
                    lambda x: calculate_contribution(x['avg_sku_change'], x['total_avg_sku_change']), axis=1
                )
                df['sales_rate_contribution'] = df.apply(
                    lambda x: calculate_contribution(x['sales_rate_change'], x['total_sales_rate_change']), axis=1
                )
                df['avg_gmv_contribution'] = df.apply(
                    lambda x: calculate_contribution(x['avg_gmv_change'], x['total_avg_gmv_change']), axis=1
                )
                df['total_gmv_contribution'] = df.apply(
                    lambda x: calculate_contribution(x['total_gmv_change'], x['total_total_gmv_change']), axis=1
                )
        
        return df
        
    except Exception as e:
        print(f"获取供给向指标整体数据失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def get_marketing_side_indicators_result(flag, brand, tar_date, base_date, sub_brand, province, city,
                                        vender, platform='全部', upc='全部', coupon_mechanism='全部',
                                        coupon_threshold='全部', coupon_discount='全部', attr_index='全量GMV'):
    """
    获取促销项指标数据
    促销项指标关系：
    GMV = 去重活动GMV + 自然GMV
    GMV = 活动机制GMV + 自然GMV
    GMV = 活动机制核销金额 × 活动机制活动ROI + 自然GMV

    注：
    - 自然GMV = 总GMV - 去重活动GMV
    - 活动GMV下钻功能使用activity_gmv字段进行机制级别的数据分析

    Args:
        flag: 时间类型标识 (1=单日期, 2=日期范围)
        brand: 品牌
        tar_date: 目标日期
        base_date: 基准日期
        sub_brand: 子品牌
        province: 省份
        city: 城市
        vender: 零售商
        platform: 平台
        upc: 商品
        coupon_mechanism: 券机制
        coupon_threshold: 券门槛
        coupon_discount: 券折扣
        attr_index: 归因指标（全量GMV或活动GMV）

    Returns:
        dict: 促销项指标数据
    """
    try:
        # 获取正确的表名
        activity_table_name = get_activity_analysis_table(brand)
        gmv_table_name = get_gmv_table(brand)
        print(f"促销项指标查询使用的活动表名: {activity_table_name}")
        print(f"促销项指标查询使用的GMV表名: {gmv_table_name}")

        # 构建筛选条件 - 分别为GMV表和活动表构建条件
        # GMV表条件（使用brand_filter_condition）
        gmv_conditions = [f"brand_filter_condition = '{brand}'"]

        # 活动表条件（使用brand）
        activity_conditions = [f"brand = '{brand}'"]

        if sub_brand != '全部':
            condition = build_multi_select_condition('sub_brand', sub_brand)
            if condition:
                # 只删除开头的"and "，避免影响字段名中的"and"
                clean_condition = condition[4:] if condition.startswith('and ') else condition
                gmv_conditions.append(clean_condition)
                activity_conditions.append(clean_condition)

        if province != '全部':
            condition = build_multi_select_condition('province', province)
            if condition:
                # 只删除开头的"and "，避免影响字段名中的"and"
                clean_condition = condition[4:] if condition.startswith('and ') else condition
                gmv_conditions.append(clean_condition)
                activity_conditions.append(clean_condition)

        if city != '全部':
            # GMV表和活动分析表都使用standard_city字段
            condition = build_multi_select_condition('standard_city', city)
            if condition:
                # 只删除开头的"and "，避免影响字段名中的"and"
                clean_condition = condition[4:] if condition.startswith('and ') else condition
                gmv_conditions.append(clean_condition)
                activity_conditions.append(clean_condition)

        if vender != '全部':
            condition = build_multi_select_condition('vender_name', vender)
            if condition:
                # 只删除开头的"and "，避免影响字段名中的"and"
                clean_condition = condition[4:] if condition.startswith('and ') else condition
                gmv_conditions.append(clean_condition)
                activity_conditions.append(clean_condition)

        if platform != '全部':
            condition = build_multi_select_condition('platform', platform)
            if condition:
                # 只删除开头的"and "，避免影响字段名中的"and"
                clean_condition = condition[4:] if condition.startswith('and ') else condition
                gmv_conditions.append(clean_condition)
                activity_conditions.append(clean_condition)

        if upc != '全部':
            condition = build_multi_select_condition('upc', upc)
            if condition:
                # 只删除开头的"and "，避免影响字段名中的"and"
                clean_condition = condition[4:] if condition.startswith('and ') else condition
                gmv_conditions.append(clean_condition)
                activity_conditions.append(clean_condition)

        if coupon_mechanism != '全部':
            condition = build_multi_select_condition('coupon_name', coupon_mechanism)
            if condition:
                # 只添加到活动表条件，GMV表没有券相关字段
                clean_condition = condition[4:] if condition.startswith('and ') else condition
                activity_conditions.append(clean_condition)

        if coupon_threshold != '全部':
            condition = build_multi_select_condition('coupon_threshold', coupon_threshold)
            if condition:
                # 只添加到活动表条件，GMV表没有券相关字段
                clean_condition = condition[4:] if condition.startswith('and ') else condition
                activity_conditions.append(clean_condition)

        if coupon_discount != '全部':
            condition = build_multi_select_condition('coupon_discount', coupon_discount)
            if condition:
                # 只添加到活动表条件，GMV表没有券相关字段
                clean_condition = condition[4:] if condition.startswith('and ') else condition
                activity_conditions.append(clean_condition)
        
        # 构建日期条件 - GMV表使用YYYY-MM-DD格式，活动表使用YYYYMMDD格式
        if flag == 1:  # 单日期模式
            # GMV表日期格式：YYYY-MM-DD
            gmv_tar_date = tar_date if '-' in tar_date else f"{tar_date[:4]}-{tar_date[4:6]}-{tar_date[6:8]}"
            gmv_base_date = base_date if '-' in base_date else f"{base_date[:4]}-{base_date[4:6]}-{base_date[6:8]}"
            gmv_date_condition = f"ds IN ('{gmv_tar_date}', '{gmv_base_date}')"

            # 活动表日期格式：YYYYMMDD
            activity_tar_date = tar_date.replace('-', '') if '-' in tar_date else tar_date
            activity_base_date = base_date.replace('-', '') if '-' in base_date else base_date
            activity_date_condition = f"ds IN ('{activity_tar_date}', '{activity_base_date}')"
        else:  # 日期范围模式
            if '至' in tar_date:
                tar_parts = tar_date.split('至')
                tar_start_orig = tar_parts[0]
                tar_end_orig = tar_parts[1]
            else:
                tar_start_orig = tar_date
                tar_end_orig = tar_date

            if '至' in base_date:
                base_parts = base_date.split('至')
                base_start_orig = base_parts[0]
                base_end_orig = base_parts[1]
            else:
                base_start_orig = base_date
                base_end_orig = base_date

            # GMV表日期格式：YYYY-MM-DD
            gmv_tar_start = tar_start_orig if '-' in tar_start_orig else f"{tar_start_orig[:4]}-{tar_start_orig[4:6]}-{tar_start_orig[6:8]}"
            gmv_tar_end = tar_end_orig if '-' in tar_end_orig else f"{tar_end_orig[:4]}-{tar_end_orig[4:6]}-{tar_end_orig[6:8]}"
            gmv_base_start = base_start_orig if '-' in base_start_orig else f"{base_start_orig[:4]}-{base_start_orig[4:6]}-{base_start_orig[6:8]}"
            gmv_base_end = base_end_orig if '-' in base_end_orig else f"{base_end_orig[:4]}-{base_end_orig[4:6]}-{base_end_orig[6:8]}"
            gmv_date_condition = f"(ds BETWEEN '{gmv_tar_start}' AND '{gmv_tar_end}' OR ds BETWEEN '{gmv_base_start}' AND '{gmv_base_end}')"

            # 活动表日期格式：YYYYMMDD
            activity_tar_start = tar_start_orig.replace('-', '') if '-' in tar_start_orig else tar_start_orig
            activity_tar_end = tar_end_orig.replace('-', '') if '-' in tar_end_orig else tar_end_orig
            activity_base_start = base_start_orig.replace('-', '') if '-' in base_start_orig else base_start_orig
            activity_base_end = base_end_orig.replace('-', '') if '-' in base_end_orig else base_end_orig
            activity_date_condition = f"(ds BETWEEN '{activity_tar_start}' AND '{activity_tar_end}' OR ds BETWEEN '{activity_base_start}' AND '{activity_base_end}')"

        # 构建完整的WHERE子句
        gmv_conditions.append(gmv_date_condition)
        activity_conditions.append(activity_date_condition)
        gmv_where_clause = ' AND '.join(gmv_conditions)
        activity_where_clause = ' AND '.join(activity_conditions)
        
        # 构建SQL查询 - 需要同时查询GMV表和活动表
        if flag == 1:  # 单日期模式
            sql = f"""
            WITH gmv_data AS (
                SELECT
                    ds,
                    SUM(COALESCE(gmv, 0)) as total_gmv
                FROM {gmv_table_name}
                WHERE {gmv_where_clause}
                GROUP BY ds
            ),
            activity_data AS (
                SELECT
                    ds,
                    SUM(COALESCE(activity_gmv, 0)) as total_activity_gmv,
                    -- 从活动分析表获取去重活动GMV，如果字段不存在则使用activity_gmv
                    SUM(COALESCE(activity_gmv_remove_dup, activity_gmv, 0)) as total_activity_gmv_dedup,
                    SUM(COALESCE(activity_expense, 0)) as total_activity_expense
                FROM {activity_table_name}
                WHERE {activity_where_clause}
                GROUP BY ds
            ),
            -- 将活动表的日期格式转换为GMV表格式以便连接
            activity_data_formatted AS (
                SELECT
                    CONCAT(SUBSTR(ds, 1, 4), '-', SUBSTR(ds, 5, 2), '-', SUBSTR(ds, 7, 2)) as ds_formatted,
                    total_activity_gmv,
                    total_activity_gmv_dedup,
                    total_activity_expense
                FROM activity_data
            ),
            filtered_data AS (
                SELECT
                    COALESCE(g.ds, a.ds_formatted) as ds,
                    COALESCE(g.total_gmv, 0) as total_gmv,
                    COALESCE(a.total_activity_gmv, 0) as total_activity_gmv,
                    COALESCE(a.total_activity_gmv_dedup, 0) as total_activity_gmv_dedup,
                    COALESCE(a.total_activity_expense, 0) as total_activity_expense
                FROM gmv_data g
                FULL OUTER JOIN activity_data_formatted a ON g.ds = a.ds_formatted
            ),
            target_data AS (
                SELECT
                    COALESCE(total_gmv, 0) as target_total_gmv,
                    COALESCE(total_activity_gmv, 0) as target_activity_gmv,
                    COALESCE(total_activity_gmv_dedup, 0) as target_activity_gmv_dedup,
                    COALESCE(total_activity_expense, 0) as target_activity_expense,
                    COALESCE(total_gmv, 0) - COALESCE(total_activity_gmv_dedup, 0) as target_natural_gmv
                FROM filtered_data
                WHERE ds = '{gmv_tar_date}'
            ),
            base_data AS (
                SELECT
                    COALESCE(total_gmv, 0) as base_total_gmv,
                    COALESCE(total_activity_gmv, 0) as base_activity_gmv,
                    COALESCE(total_activity_gmv_dedup, 0) as base_activity_gmv_dedup,
                    COALESCE(total_activity_expense, 0) as base_activity_expense,
                    COALESCE(total_gmv, 0) - COALESCE(total_activity_gmv_dedup, 0) as base_natural_gmv
                FROM filtered_data
                WHERE ds = '{gmv_base_date}'
            )
            SELECT
                -- 目标期数据
                COALESCE(t.target_total_gmv, 0) as target_total_gmv,
                COALESCE(t.target_activity_gmv, 0) as target_activity_gmv,
                COALESCE(t.target_activity_gmv_dedup, 0) as target_activity_gmv_dedup,
                COALESCE(t.target_activity_expense, 0) as target_activity_expense,
                COALESCE(t.target_natural_gmv, 0) as target_natural_gmv,
                CASE WHEN COALESCE(t.target_activity_expense, 0) > 0
                     THEN COALESCE(t.target_activity_gmv, 0) / COALESCE(t.target_activity_expense, 0)
                     ELSE 0 END as target_activity_roi,

                -- 基准期数据
                COALESCE(b.base_total_gmv, 0) as base_total_gmv,
                COALESCE(b.base_activity_gmv, 0) as base_activity_gmv,
                COALESCE(b.base_activity_gmv_dedup, 0) as base_activity_gmv_dedup,
                COALESCE(b.base_activity_expense, 0) as base_activity_expense,
                COALESCE(b.base_natural_gmv, 0) as base_natural_gmv,
                CASE WHEN COALESCE(b.base_activity_expense, 0) > 0
                     THEN COALESCE(b.base_activity_gmv, 0) / COALESCE(b.base_activity_expense, 0)
                     ELSE 0 END as base_activity_roi
            FROM target_data t
            CROSS JOIN base_data b
            """
        else:  # 日期范围模式
            sql = f"""
            WITH gmv_data AS (
                SELECT
                    CASE WHEN ds BETWEEN '{gmv_tar_start}' AND '{gmv_tar_end}' THEN 'target'
                         WHEN ds BETWEEN '{gmv_base_start}' AND '{gmv_base_end}' THEN 'base'
                         ELSE 'other' END as period_type,
                    SUM(COALESCE(gmv, 0)) as total_gmv
                FROM {gmv_table_name}
                WHERE {gmv_where_clause}
                GROUP BY CASE WHEN ds BETWEEN '{gmv_tar_start}' AND '{gmv_tar_end}' THEN 'target'
                              WHEN ds BETWEEN '{gmv_base_start}' AND '{gmv_base_end}' THEN 'base'
                              ELSE 'other' END
            ),
            activity_data AS (
                SELECT
                    CASE WHEN ds BETWEEN '{activity_tar_start}' AND '{activity_tar_end}' THEN 'target'
                         WHEN ds BETWEEN '{activity_base_start}' AND '{activity_base_end}' THEN 'base'
                         ELSE 'other' END as period_type,
                    SUM(COALESCE(activity_gmv, 0)) as total_activity_gmv,
                    -- 从活动分析表获取去重活动GMV，如果字段不存在则使用activity_gmv
                    SUM(COALESCE(activity_gmv_remove_dup, activity_gmv, 0)) as total_activity_gmv_dedup,
                    SUM(COALESCE(activity_expense, 0)) as total_activity_expense
                FROM {activity_table_name}
                WHERE {activity_where_clause}
                GROUP BY CASE WHEN ds BETWEEN '{activity_tar_start}' AND '{activity_tar_end}' THEN 'target'
                              WHEN ds BETWEEN '{activity_base_start}' AND '{activity_base_end}' THEN 'base'
                              ELSE 'other' END
            ),
            filtered_data AS (
                SELECT
                    COALESCE(g.period_type, a.period_type) as period_type,
                    COALESCE(g.total_gmv, 0) as total_gmv,
                    COALESCE(a.total_activity_gmv, 0) as total_activity_gmv,
                    COALESCE(a.total_activity_gmv_dedup, 0) as total_activity_gmv_dedup,
                    COALESCE(a.total_activity_expense, 0) as total_activity_expense
                FROM gmv_data g
                FULL OUTER JOIN activity_data a ON g.period_type = a.period_type
            ),
            target_data AS (
                SELECT 
                    COALESCE(total_gmv, 0) as target_total_gmv,
                    COALESCE(total_activity_gmv, 0) as target_activity_gmv,
                    COALESCE(total_activity_gmv_dedup, 0) as target_activity_gmv_dedup,
                    COALESCE(total_activity_expense, 0) as target_activity_expense,
                    COALESCE(total_gmv, 0) - COALESCE(total_activity_gmv_dedup, 0) as target_natural_gmv
                FROM filtered_data
                WHERE period_type = 'target'
            ),
            base_data AS (
                SELECT 
                    COALESCE(total_gmv, 0) as base_total_gmv,
                    COALESCE(total_activity_gmv, 0) as base_activity_gmv,
                    COALESCE(total_activity_gmv_dedup, 0) as base_activity_gmv_dedup,
                    COALESCE(total_activity_expense, 0) as base_activity_expense,
                    COALESCE(total_gmv, 0) - COALESCE(total_activity_gmv_dedup, 0) as base_natural_gmv
                FROM filtered_data
                WHERE period_type = 'base'
            )
            SELECT 
                -- 目标期数据
                COALESCE(t.target_total_gmv, 0) as target_total_gmv,
                COALESCE(t.target_activity_gmv, 0) as target_activity_gmv,
                COALESCE(t.target_activity_gmv_dedup, 0) as target_activity_gmv_dedup,
                COALESCE(t.target_activity_expense, 0) as target_activity_expense,
                COALESCE(t.target_natural_gmv, 0) as target_natural_gmv,
                CASE WHEN COALESCE(t.target_activity_expense, 0) > 0 
                     THEN COALESCE(t.target_activity_gmv, 0) / COALESCE(t.target_activity_expense, 0)
                     ELSE 0 END as target_activity_roi,
                
                -- 基准期数据
                COALESCE(b.base_total_gmv, 0) as base_total_gmv,
                COALESCE(b.base_activity_gmv, 0) as base_activity_gmv,
                COALESCE(b.base_activity_gmv_dedup, 0) as base_activity_gmv_dedup,
                COALESCE(b.base_activity_expense, 0) as base_activity_expense,
                COALESCE(b.base_natural_gmv, 0) as base_natural_gmv,
                CASE WHEN COALESCE(b.base_activity_expense, 0) > 0 
                     THEN COALESCE(b.base_activity_gmv, 0) / COALESCE(b.base_activity_expense, 0)
                     ELSE 0 END as base_activity_roi
            FROM target_data t
            CROSS JOIN base_data b
            """
        
        print(f"促销项指标查询SQL: {sql}")
        
        df = get_row_data(sql)
        
        if df is not None and not df.empty:
            # 计算变化值、变化率、贡献度
            row = df.iloc[0]
            
            # 总GMV
            total_gmv_change = row['target_total_gmv'] - row['base_total_gmv']
            total_gmv_change_rate = (total_gmv_change / row['base_total_gmv']) if row['base_total_gmv'] > 0 else 0
            
            # 活动GMV
            activity_gmv_change = row['target_activity_gmv'] - row['base_activity_gmv']
            activity_gmv_change_rate = (activity_gmv_change / row['base_activity_gmv']) if row['base_activity_gmv'] > 0 else 0
            activity_gmv_contribution = (activity_gmv_change / total_gmv_change * 100) if total_gmv_change != 0 else 100
            
            # 活动GMV下钻（原去重活动GMV逻辑，现改为使用activity_gmv）
            activity_gmv_dedup_change = row['target_activity_gmv_dedup'] - row['base_activity_gmv_dedup']
            activity_gmv_dedup_change_rate = (activity_gmv_dedup_change / row['base_activity_gmv_dedup']) if row['base_activity_gmv_dedup'] > 0 else 0
            activity_gmv_dedup_contribution = (activity_gmv_dedup_change / total_gmv_change * 100) if total_gmv_change != 0 else 100
            
            # 使用LMDI算法计算促销项指标贡献度
            activity_expense_change = row['target_activity_expense'] - row['base_activity_expense']
            activity_expense_change_rate = (activity_expense_change / row['base_activity_expense']) if row['base_activity_expense'] > 0 else 0

            natural_gmv_change = row['target_natural_gmv'] - row['base_natural_gmv']
            natural_gmv_change_rate = (natural_gmv_change / row['base_natural_gmv']) if row['base_natural_gmv'] > 0 else 0

            activity_roi_change = row['target_activity_roi'] - row['base_activity_roi']
            activity_roi_change_rate = (activity_roi_change / row['base_activity_roi']) if row['base_activity_roi'] > 0 else 0

            # 尝试使用LMDI算法计算贡献度
            lmdi_success = False

            if attr_index == '活动GMV':
                # 活动GMV归因：GMV = 活动机制核销金额 × 活动机制活动ROI
                Y0 = row['base_activity_gmv']
                Y1 = row['target_activity_gmv']
                A0 = row['base_activity_expense']
                A1 = row['target_activity_expense']
                B0 = row['base_activity_roi']
                B1 = row['target_activity_roi']

                print(f"促销项LMDI 2因子参数: Y0={Y0}, Y1={Y1}, A0={A0}, A1={A1}, B0={B0}, B1={B1}")

                if all(val > 0 for val in [Y0, Y1, A0, A1, B0, B1]):
                    lmdi_result = calculate_lmdi_contribution_2_factors(Y0, Y1, A0, A1, B0, B1)

                    if lmdi_result:
                        activity_expense_contribution = lmdi_result['因子A']['相对贡献度'] * 100
                        activity_roi_contribution = lmdi_result['因子B']['相对贡献度'] * 100
                        natural_gmv_contribution = 0  # 活动GMV归因中没有自然GMV
                        lmdi_success = True

                        # 验证活动机制核销金额和活动机制活动ROI贡献度相加是否等于100%或-100%
                        total_contribution = activity_expense_contribution + activity_roi_contribution
                        print(f"促销项LMDI 2因子计算成功: 核销金额贡献度={activity_expense_contribution:.2f}%, ROI贡献度={activity_roi_contribution:.2f}%, 总和={total_contribution:.2f}%")

                        # 由于LMDI算法已经确保了相对贡献度总和为1.0或-1.0，转换为百分比后应该严格等于100%或-100%
                        # 但为了避免浮点数精度问题，我们进行最终调整
                        target_sum = 100.0 if total_contribution > 0 else -100.0
                        if abs(total_contribution - target_sum) > 1e-10:
                            # 将误差分配给第一个因子（活动机制核销金额）
                            activity_expense_contribution = target_sum - activity_roi_contribution
                            print(f"浮点数精度调整: 核销金额贡献度={activity_expense_contribution:.2f}%, ROI贡献度={activity_roi_contribution:.2f}%, 总和={activity_expense_contribution + activity_roi_contribution:.2f}%")
            else:
                # 全量GMV归因：GMV = 活动机制核销金额 × 活动机制活动ROI + 自然GMV（自然GMV = 总GMV - 去重活动GMV）
                Y0 = row['base_total_gmv']
                Y1 = row['target_total_gmv']
                A0 = row['base_activity_expense']
                A1 = row['target_activity_expense']
                B0 = row['base_activity_roi']
                B1 = row['target_activity_roi']
                C0 = row['base_natural_gmv']
                C1 = row['target_natural_gmv']

                print(f"促销项LMDI 3因子参数: Y0={Y0}, Y1={Y1}, A0={A0}, A1={A1}, B0={B0}, B1={B1}, C0={C0}, C1={C1}")

                if all(val > 0 for val in [Y0, Y1, A0, A1, B0, B1]) and C0 >= 0 and C1 >= 0:
                    lmdi_result = calculate_lmdi_contribution_3_factors(Y0, Y1, A0, A1, B0, B1, C0, C1)

                    if lmdi_result:
                        # 计算各部分的变化值
                        activity_gmv_dedup_change = row['target_activity_gmv_dedup'] - row['base_activity_gmv_dedup']  # 去重活动GMV变化
                        natural_gmv_change = C1 - C0  # 自然GMV变化
                        total_gmv_change = Y1 - Y0  # 总GMV变化

                        # 使用简单的变化值比例计算贡献度
                        if total_gmv_change != 0:
                            # 计算原始贡献度比例（变化值 / 总变化值）
                            natural_gmv_contribution_ratio = natural_gmv_change / total_gmv_change
                            activity_gmv_dedup_contribution_ratio = activity_gmv_dedup_change / total_gmv_change

                            # 根据大盘涨跌决定贡献度总和的符号
                            if total_gmv_change > 0:
                                # 大盘上涨，贡献度总和为100%
                                natural_gmv_contribution = natural_gmv_contribution_ratio * 100
                                activity_gmv_dedup_contribution = activity_gmv_dedup_contribution_ratio * 100
                            else:
                                # 大盘下跌，贡献度总和为-100%
                                natural_gmv_contribution = -abs(natural_gmv_contribution_ratio) * 100
                                activity_gmv_dedup_contribution = -abs(activity_gmv_dedup_contribution_ratio) * 100

                            # 验证两者相加是否等于100%或-100%
                            total_contribution = natural_gmv_contribution + activity_gmv_dedup_contribution
                            print(f"贡献度计算: 自然GMV={natural_gmv_contribution:.2f}%, 去重活动GMV={activity_gmv_dedup_contribution:.2f}%, 总和={total_contribution:.2f}%")

                            # 确保总和严格等于100%或-100%
                            target_sum = 100.0 if total_gmv_change > 0 else -100.0
                            if abs(total_contribution - target_sum) > 1e-10:
                                # 将误差分配给去重活动GMV
                                activity_gmv_dedup_contribution = target_sum - natural_gmv_contribution
                                print(f"精度调整后: 自然GMV={natural_gmv_contribution:.2f}%, 去重活动GMV={activity_gmv_dedup_contribution:.2f}%, 总和={natural_gmv_contribution + activity_gmv_dedup_contribution:.2f}%")
                        else:
                            natural_gmv_contribution = 0.0
                            activity_gmv_dedup_contribution = 0.0

                        # 计算活动机制核销金额和活动机制活动ROI相对于活动GMV变化的贡献度
                        # 使用计算出的活动GMV变化（A×B的变化）
                        calculated_activity_gmv_change = (A1 * B1) - (A0 * B0)
                        if calculated_activity_gmv_change != 0:
                            # 使用2因子LMDI算法计算活动GMV内部的贡献度分配
                            activity_lmdi = calculate_lmdi_contribution_2_factors(A0 * B0, A1 * B1, A0, A1, B0, B1)
                            if activity_lmdi:
                                activity_expense_contribution = activity_lmdi['因子A']['相对贡献度'] * 100
                                activity_roi_contribution = activity_lmdi['因子B']['相对贡献度'] * 100

                                # 确保活动机制贡献度总和为100%或-100%
                                mechanism_total = activity_expense_contribution + activity_roi_contribution
                                target_sum = 100.0 if mechanism_total > 0 else -100.0
                                if abs(mechanism_total - target_sum) > 1e-10:
                                    activity_expense_contribution = target_sum - activity_roi_contribution

                                print(f"活动机制内部贡献度: 核销金额={activity_expense_contribution:.2f}%, ROI={activity_roi_contribution:.2f}%, 总和={activity_expense_contribution + activity_roi_contribution:.2f}%")
                            else:
                                # 如果LMDI失败，使用简单分配
                                activity_expense_contribution = 50.0 if calculated_activity_gmv_change > 0 else -50.0
                                activity_roi_contribution = 50.0 if calculated_activity_gmv_change > 0 else -50.0
                        else:
                            activity_expense_contribution = 0.0
                            activity_roi_contribution = 0.0

                        lmdi_success = True

            # 如果LMDI计算失败，回退到传统方法
            if not lmdi_success:
                print("促销项LMDI计算失败，使用传统方法")
                if total_gmv_change != 0:
                    if attr_index == '活动GMV':
                        # 活动GMV归因：只计算活动机制核销金额和活动机制活动ROI的贡献度
                        activity_expense_contribution = (activity_expense_change / total_gmv_change * 100)
                        activity_roi_contribution = (activity_roi_change / total_gmv_change * 100)
                        natural_gmv_contribution = 0  # 活动GMV归因中没有自然GMV

                        # 根据大盘涨跌调整贡献度总和
                        current_sum = activity_expense_contribution + activity_roi_contribution
                        print(f"传统方法(活动GMV)调整前: 核销金额={activity_expense_contribution:.2f}%, ROI={activity_roi_contribution:.2f}%, 总和={current_sum:.2f}%")

                        if current_sum != 0:  # 避免除零错误
                            if total_gmv_change > 0:  # 大盘上涨
                                target_sum = 100.0
                            else:  # 大盘下跌
                                target_sum = -100.0

                            # 按比例调整各因子贡献度
                            adjustment_factor = target_sum / current_sum
                            activity_expense_contribution = activity_expense_contribution * adjustment_factor
                            activity_roi_contribution = activity_roi_contribution * adjustment_factor

                            # 确保贡献度总和严格等于目标值（避免浮点数精度问题）
                            actual_sum = activity_expense_contribution + activity_roi_contribution
                            if abs(actual_sum - target_sum) > 1e-10:
                                # 将误差分配给第一个因子
                                activity_expense_contribution = target_sum - activity_roi_contribution

                            print(f"传统方法(活动GMV)调整后: 核销金额={activity_expense_contribution:.2f}%, ROI={activity_roi_contribution:.2f}%, 总和={activity_expense_contribution + activity_roi_contribution:.2f}%")
                    else:
                        # 全量GMV归因：使用简单的变化值比例计算贡献度
                        # 计算各部分的变化值
                        activity_gmv_dedup_change = row['target_activity_gmv_dedup'] - row['base_activity_gmv_dedup']  # 去重活动GMV变化

                        # 使用简单的变化值比例计算贡献度
                        if total_gmv_change != 0:
                            # 计算原始贡献度比例（变化值 / 总变化值）
                            natural_gmv_contribution_ratio = natural_gmv_change / total_gmv_change
                            activity_gmv_dedup_contribution_ratio = activity_gmv_dedup_change / total_gmv_change

                            # 根据大盘涨跌决定贡献度总和的符号
                            if total_gmv_change > 0:
                                # 大盘上涨，贡献度总和为100%
                                natural_gmv_contribution = natural_gmv_contribution_ratio * 100
                                activity_gmv_dedup_contribution = activity_gmv_dedup_contribution_ratio * 100
                            else:
                                # 大盘下跌，贡献度总和为-100%
                                natural_gmv_contribution = -abs(natural_gmv_contribution_ratio) * 100
                                activity_gmv_dedup_contribution = -abs(activity_gmv_dedup_contribution_ratio) * 100

                            # 验证两者相加是否等于100%或-100%
                            total_contribution = natural_gmv_contribution + activity_gmv_dedup_contribution
                            print(f"传统方法贡献度计算: 自然GMV={natural_gmv_contribution:.2f}%, 去重活动GMV={activity_gmv_dedup_contribution:.2f}%, 总和={total_contribution:.2f}%")

                            # 确保总和严格等于100%或-100%
                            target_sum = 100.0 if total_gmv_change > 0 else -100.0
                            if abs(total_contribution - target_sum) > 1e-10:
                                # 将误差分配给去重活动GMV
                                activity_gmv_dedup_contribution = target_sum - natural_gmv_contribution
                                print(f"传统方法精度调整后: 自然GMV={natural_gmv_contribution:.2f}%, 去重活动GMV={activity_gmv_dedup_contribution:.2f}%, 总和={natural_gmv_contribution + activity_gmv_dedup_contribution:.2f}%")
                        else:
                            natural_gmv_contribution = 0.0
                            activity_gmv_dedup_contribution = 0.0

                        # 计算活动机制核销金额和活动机制活动ROI相对于活动GMV变化的贡献度
                        # 使用计算出的活动GMV变化（A×B的变化）
                        calculated_activity_gmv_change = (row['target_activity_expense'] * row['target_activity_roi']) - (row['base_activity_expense'] * row['base_activity_roi'])
                        if calculated_activity_gmv_change != 0:
                            # 使用简单的比例分配
                            expense_ratio = activity_expense_change / calculated_activity_gmv_change
                            roi_ratio = activity_roi_change / calculated_activity_gmv_change

                            # 确保比例总和为1
                            total_ratio = expense_ratio + roi_ratio
                            if total_ratio != 0:
                                expense_ratio = expense_ratio / total_ratio
                                roi_ratio = roi_ratio / total_ratio
                            else:
                                expense_ratio = 0.5
                                roi_ratio = 0.5

                            # 计算相对于活动GMV的贡献度
                            if calculated_activity_gmv_change > 0:
                                activity_expense_contribution = expense_ratio * 100
                                activity_roi_contribution = roi_ratio * 100
                            else:
                                activity_expense_contribution = expense_ratio * -100
                                activity_roi_contribution = roi_ratio * -100

                            # 确保贡献度总和严格等于±100%
                            mechanism_total = activity_expense_contribution + activity_roi_contribution
                            target_sum = 100.0 if mechanism_total > 0 else -100.0
                            if abs(mechanism_total - target_sum) > 1e-10:
                                activity_expense_contribution = target_sum - activity_roi_contribution
                        else:
                            activity_expense_contribution = 0.0
                            activity_roi_contribution = 0.0

                        print(f"传统方法(活动GMV): 核销金额贡献度={activity_expense_contribution:.2f}%, ROI贡献度={activity_roi_contribution:.2f}%, 总和={activity_expense_contribution + activity_roi_contribution:.2f}%")
                else:
                    activity_expense_contribution = 100
                    natural_gmv_contribution = 0 if attr_index == '活动GMV' else 100
                    activity_roi_contribution = 100
            
            # 获取活动机制数据
            mechanisms_data = {}
            try:
                # 构建获取活动机制数据的SQL - 适配不同日期模式，添加sheet_name字段
                if flag == 1:  # 单日期模式
                    mechanism_sql = f"""
                    SELECT
                        COALESCE(coupon_name, '未知机制') as coupon_name,
                        COALESCE(sheet_name, '其他机制') as sheet_name,
                        SUM(CASE WHEN ds = '{activity_tar_date}' THEN COALESCE(activity_gmv, 0) ELSE 0 END) as target_mechanism_gmv,
                        SUM(CASE WHEN ds = '{activity_base_date}' THEN COALESCE(activity_gmv, 0) ELSE 0 END) as base_mechanism_gmv,
                        SUM(CASE WHEN ds = '{activity_tar_date}' THEN COALESCE(activity_expense, 0) ELSE 0 END) as target_mechanism_subsidy,
                        SUM(CASE WHEN ds = '{activity_base_date}' THEN COALESCE(activity_expense, 0) ELSE 0 END) as base_mechanism_subsidy,
                        CASE
                            WHEN SUM(CASE WHEN ds = '{activity_tar_date}' THEN COALESCE(activity_expense, 0) ELSE 0 END) > 0
                            THEN SUM(CASE WHEN ds = '{activity_tar_date}' THEN COALESCE(activity_gmv, 0) ELSE 0 END) /
                                 SUM(CASE WHEN ds = '{activity_tar_date}' THEN COALESCE(activity_expense, 0) ELSE 0 END)
                            ELSE 0
                        END as target_mechanism_roi,
                        CASE
                            WHEN SUM(CASE WHEN ds = '{activity_base_date}' THEN COALESCE(activity_expense, 0) ELSE 0 END) > 0
                            THEN SUM(CASE WHEN ds = '{activity_base_date}' THEN COALESCE(activity_gmv, 0) ELSE 0 END) /
                                 SUM(CASE WHEN ds = '{activity_base_date}' THEN COALESCE(activity_expense, 0) ELSE 0 END)
                            ELSE 0
                        END as base_mechanism_roi
                    FROM {activity_table_name}
                    WHERE {activity_where_clause}
                    AND coupon_name IS NOT NULL
                    AND coupon_name != ''
                    GROUP BY coupon_name, sheet_name
                    HAVING SUM(CASE WHEN ds = '{activity_tar_date}' THEN COALESCE(activity_gmv, 0) ELSE 0 END) > 0
                        OR SUM(CASE WHEN ds = '{activity_base_date}' THEN COALESCE(activity_gmv, 0) ELSE 0 END) > 0
                    ORDER BY target_mechanism_gmv DESC
                    """
                else:  # 日期范围模式
                    mechanism_sql = f"""
                    SELECT
                        COALESCE(coupon_name, '未知机制') as coupon_name,
                        COALESCE(sheet_name, '其他机制') as sheet_name,
                        SUM(CASE WHEN ds BETWEEN '{activity_tar_start}' AND '{activity_tar_end}' THEN COALESCE(activity_gmv, 0) ELSE 0 END) as target_mechanism_gmv,
                        SUM(CASE WHEN ds BETWEEN '{activity_base_start}' AND '{activity_base_end}' THEN COALESCE(activity_gmv, 0) ELSE 0 END) as base_mechanism_gmv,
                        SUM(CASE WHEN ds BETWEEN '{activity_tar_start}' AND '{activity_tar_end}' THEN COALESCE(activity_expense, 0) ELSE 0 END) as target_mechanism_subsidy,
                        SUM(CASE WHEN ds BETWEEN '{activity_base_start}' AND '{activity_base_end}' THEN COALESCE(activity_expense, 0) ELSE 0 END) as base_mechanism_subsidy,
                        CASE
                            WHEN SUM(CASE WHEN ds BETWEEN '{activity_tar_start}' AND '{activity_tar_end}' THEN COALESCE(activity_expense, 0) ELSE 0 END) > 0
                            THEN SUM(CASE WHEN ds BETWEEN '{activity_tar_start}' AND '{activity_tar_end}' THEN COALESCE(activity_gmv, 0) ELSE 0 END) /
                                 SUM(CASE WHEN ds BETWEEN '{activity_tar_start}' AND '{activity_tar_end}' THEN COALESCE(activity_expense, 0) ELSE 0 END)
                            ELSE 0
                        END as target_mechanism_roi,
                        CASE
                            WHEN SUM(CASE WHEN ds BETWEEN '{activity_base_start}' AND '{activity_base_end}' THEN COALESCE(activity_expense, 0) ELSE 0 END) > 0
                            THEN SUM(CASE WHEN ds BETWEEN '{activity_base_start}' AND '{activity_base_end}' THEN COALESCE(activity_gmv, 0) ELSE 0 END) /
                                 SUM(CASE WHEN ds BETWEEN '{activity_base_start}' AND '{activity_base_end}' THEN COALESCE(activity_expense, 0) ELSE 0 END)
                            ELSE 0
                        END as base_mechanism_roi
                    FROM {activity_table_name}
                    WHERE {activity_where_clause}
                    AND coupon_name IS NOT NULL
                    AND coupon_name != ''
                    GROUP BY coupon_name, sheet_name
                    HAVING SUM(CASE WHEN ds BETWEEN '{activity_tar_start}' AND '{activity_tar_end}' THEN COALESCE(activity_gmv, 0) ELSE 0 END) > 0
                        OR SUM(CASE WHEN ds BETWEEN '{activity_base_start}' AND '{activity_base_end}' THEN COALESCE(activity_gmv, 0) ELSE 0 END) > 0
                    ORDER BY target_mechanism_gmv DESC
                    """
                
                print(f"获取活动机制数据SQL: {mechanism_sql}")
                
                mechanism_df = get_row_data(mechanism_sql)
                
                if mechanism_df is not None and not mechanism_df.empty:
                    for _, mech_row in mechanism_df.iterrows():
                        mechanism_name = mech_row['coupon_name'] or '未知机制'
                        mechanism_type = mech_row['sheet_name'] or '其他机制'
                        mech_target_gmv = mech_row['target_mechanism_gmv'] or 0
                        mech_base_gmv = mech_row['base_mechanism_gmv'] or 0
                        mech_target_subsidy = mech_row['target_mechanism_subsidy'] or 0
                        mech_base_subsidy = mech_row['base_mechanism_subsidy'] or 0
                        
                        # 计算变化值和变化率
                        mech_gmv_change = mech_target_gmv - mech_base_gmv
                        mech_gmv_change_rate = (mech_gmv_change / mech_base_gmv * 100) if mech_base_gmv != 0 else 0
                        mech_subsidy_change = mech_target_subsidy - mech_base_subsidy
                        mech_subsidy_change_rate = (mech_subsidy_change / mech_base_subsidy * 100) if mech_base_subsidy != 0 else 0
                        
                        # 计算ROI
                        mech_target_roi = mech_target_gmv / mech_target_subsidy if mech_target_subsidy != 0 else 0
                        mech_base_roi = mech_base_gmv / mech_base_subsidy if mech_base_subsidy != 0 else 0
                        mech_roi_change = mech_target_roi - mech_base_roi
                        mech_roi_change_rate = (mech_roi_change / mech_base_roi * 100) if mech_base_roi != 0 else 0
                        
                        # 计算GMV贡献度（使用修正的贡献度计算规则）
                        if total_gmv_change != 0:
                            # 先检查该机制类型中有多少个机制
                            # 这里我们需要在后续的机制类型分组中处理，暂时使用原有逻辑
                            # 计算原始贡献度比例
                            mech_contribution_ratio = mech_gmv_change / total_gmv_change

                            # 根据大盘涨跌决定贡献度符号
                            if total_gmv_change > 0:
                                # 大盘上涨，使用原始比例
                                mech_contribution = mech_contribution_ratio * 100
                            else:
                                # 大盘下跌，使用绝对值比例并加负号
                                mech_contribution = -abs(mech_contribution_ratio) * 100
                        else:
                            mech_contribution = 0
                        
                        # 计算核销金额贡献度（相对于总核销金额变化）
                        total_subsidy_change = row['target_activity_expense'] - row['base_activity_expense']
                        mech_subsidy_contribution = (mech_subsidy_change / total_subsidy_change * 100) if total_subsidy_change != 0 else 0
                        
                        # 计算ROI贡献度（相对于总ROI变化）
                        total_roi_change = row['target_activity_roi'] - row['base_activity_roi']
                        mech_roi_contribution = (mech_roi_change / total_roi_change * 100) if total_roi_change != 0 else 0
                        
                        mechanisms_data[mechanism_name] = {
                            "机制类型": mechanism_type,  # 添加机制类型字段
                            "当期GMV": f"{round(mech_target_gmv, 2):,.0f}",  # 统一字段名称并格式化
                            "对比期GMV": f"{round(mech_base_gmv, 2):,.0f}",  # 统一字段名称并格式化
                            "GMV变化值": f"{round(mech_gmv_change, 2):,.0f}",  # 统一字段名称并格式化
                            "变化率": f"{mech_gmv_change_rate:.2f}%",
                            "贡献度": f"{mech_contribution:.2f}%",
                            "当期核销金额": f"{round(mech_target_subsidy, 2):,.0f}",  # 格式化
                            "对比期核销金额": f"{round(mech_base_subsidy, 2):,.0f}",  # 格式化
                            "核销金额变化值": f"{round(mech_subsidy_change, 2):,.0f}",  # 格式化
                            "核销金额变化率": f"{mech_subsidy_change_rate:.2f}%",
                            "核销金额贡献度": f"{mech_subsidy_contribution:.2f}%",
                            "当期ROI": round(mech_target_roi, 4),
                            "对比期ROI": round(mech_base_roi, 4),
                            "ROI变化值": round(mech_roi_change, 4),
                            "ROI变化率": f"{mech_roi_change_rate:.2f}%",
                            "ROI贡献度": f"{mech_roi_contribution:.2f}%"
                        }
                        
                    print(f"获取到 {len(mechanisms_data)} 个活动机制数据")

                    # 对机制数据按类型进行分组
                    mechanism_grouping = group_mechanisms_by_type(mechanisms_data)
                    mechanism_types_data = mechanism_grouping.get("mechanism_types", {})
                    mechanism_type_mapping = mechanism_grouping.get("mechanism_type_mapping", {})

                    # 将机制类型数据添加到机制数据中
                    mechanisms_data["_mechanism_types"] = mechanism_types_data
                    mechanisms_data["_mechanism_type_mapping"] = mechanism_type_mapping

                    # 调整单机制类型中机制的贡献度
                    for mechanism_name in mechanisms_data.keys():
                        if mechanism_name.startswith("_"):  # 跳过元数据
                            continue

                        mechanism_type = mechanism_type_mapping.get(mechanism_name)
                        if mechanism_type and mechanism_type in mechanism_types_data:
                            type_info = mechanism_types_data[mechanism_type]
                            # 检查该机制类型中是否只有一个机制
                            if "mechanisms" in type_info and len(type_info["mechanisms"]) == 1:
                                # 如果机制类型里只有一个机制，贡献度就是100%或-100%
                                if total_gmv_change > 0:
                                    adjusted_contribution = 100.0
                                elif total_gmv_change < 0:
                                    adjusted_contribution = -100.0
                                else:
                                    adjusted_contribution = 0.0

                                # 更新机制的贡献度
                                mechanisms_data[mechanism_name]["贡献度"] = f"{adjusted_contribution:.2f}%"
                                print(f"调整单机制类型 '{mechanism_type}' 中机制 '{mechanism_name}' 的贡献度为 {adjusted_contribution:.2f}%")

                else:
                    print("未获取到活动机制数据")

            except Exception as e:
                print(f"获取活动机制数据时出错: {e}")
                import traceback
                traceback.print_exc()
                mechanisms_data = {}
            
            # 构建结果
            result = {
                "总GMV": {
                    "当期值": round(row['target_total_gmv'], 2),
                    "对比期值": round(row['base_total_gmv'], 2),
                    "变化值": round(total_gmv_change, 2),
                    "变化率": f"{total_gmv_change_rate*100:.2f}%",
                    "贡献度": "100.00%"
                },
                "活动GMV": {
                    "当期值": round(row['target_activity_gmv'], 2),
                    "对比期值": round(row['base_activity_gmv'], 2),
                    "变化值": round(activity_gmv_change, 2),
                    "变化率": f"{activity_gmv_change_rate*100:.2f}%",
                    "贡献度": f"{activity_gmv_contribution:.2f}%"
                },
                "去重活动GMV": {
                    "当期值": round(row['target_activity_gmv_dedup'], 2),
                    "对比期值": round(row['base_activity_gmv_dedup'], 2),
                    "变化值": round(activity_gmv_dedup_change, 2),
                    "变化率": f"{activity_gmv_dedup_change_rate*100:.2f}%",
                    "贡献度": f"{activity_gmv_dedup_contribution:.2f}%",
                    "活动机制": mechanisms_data  # 添加活动机制数据以支持下钻功能
                },
                "活动机制核销金额": {
                    "当期值": round(row['target_activity_expense'], 2),
                    "对比期值": round(row['base_activity_expense'], 2),
                    "变化值": round(activity_expense_change, 2),
                    "变化率": f"{activity_expense_change_rate*100:.2f}%",
                    "贡献度": f"{activity_expense_contribution:.2f}%"
                },
                # 仅在全量GMV归因时包含自然GMV
                **({
                    "自然GMV": {
                        "当期值": round(row['target_natural_gmv'], 2),
                        "对比期值": round(row['base_natural_gmv'], 2),
                        "变化值": round(natural_gmv_change, 2),
                        "变化率": f"{natural_gmv_change_rate*100:.2f}%",
                        "贡献度": f"{natural_gmv_contribution:.2f}%"
                    }
                } if attr_index != '活动GMV' else {}),
                "活动机制活动ROI": {
                    "当期值": round(row['target_activity_roi'], 4),
                    "对比期值": round(row['base_activity_roi'], 4),
                    "变化值": round(activity_roi_change, 4),
                    "变化率": f"{activity_roi_change_rate*100:.2f}%",
                    "贡献度": f"{activity_roi_contribution:.2f}%"
                }
            }
            
            # 如果是活动GMV归因，去掉自然GMV相关指标
            if attr_index == '活动GMV':
                result.pop("自然GMV", None)
            
            print(f"促销项指标数据计算完成")
            return result
        else:
            print("促销项指标查询无结果")
            return None

    except Exception as e:
        print(f"获取促销项指标数据失败: {e}")
        import traceback
        traceback.print_exc()
        return None
def get_marketing_side_sheets_data(marketing_data, attr_index='全量GMV'):
    """
    格式化促销项指标数据为前端展示格式，并生成Excel可用的DataFrame

    Args:
        marketing_data: 促销项指标数据
        attr_index: 归因指标（全量GMV或活动GMV）

    Returns:
        dict: 格式化后的数据，包含DataFrame格式的Excel数据
    """
    try:
        if not marketing_data:
            return {}

        # 促销项指标公式说明
        if attr_index == '活动GMV':
            formula_description = {
                "关系公式": [
                    "GMV = 活动GMV",
                    "GMV = 活动机制GMV",
                    "GMV = 活动机制核销金额 × 活动机制活动ROI",
                    "活动GMV = 活动机制GMV = 活动机制核销金额 × 活动机制活动ROI"
                ],
                "指标说明": {
                    "总GMV": "全量交易金额",
                    "活动GMV": "参与活动的交易金额",
                    "活动GMV下钻": "活动GMV的机制级别详细分析",
                    "活动机制核销金额": "品牌在活动中的补贴投入",
                    "活动机制活动ROI": "活动GMV与活动补贴的比值"
                }
            }
        else:
            formula_description = {
                "关系公式": [
                    "GMV = 去重活动GMV + 自然GMV",
                    "GMV = 活动机制GMV + 自然GMV",
                    "GMV = 活动机制核销金额 × 活动机制活动ROI + 自然GMV",
                    "活动GMV = 活动机制GMV = 活动机制核销金额 × 活动机制活动ROI",
                    "自然GMV = 总GMV - 去重活动GMV"
                ],
                "指标说明": {
                    "总GMV": "全量交易金额",
                    "活动GMV": "参与活动的交易金额",
                    "活动GMV下钻": "活动GMV的机制级别详细分析",
                    "活动机制核销金额": "品牌在活动中的补贴投入",
                    "自然GMV": "非活动的自然交易金额（总GMV - 去重活动GMV）",
                    "活动机制活动ROI": "活动GMV与活动补贴的比值"
                }
            }

        # 将促销项指标数据转换为DataFrame格式，用于Excel导出
        excel_data = []

        # 根据attr_index确定指标顺序
        if attr_index == '活动GMV':
            indicator_order = ["总GMV", "活动GMV", "去重活动GMV", "活动机制核销金额", "活动机制活动ROI"]
        else:
            indicator_order = ["总GMV", "活动GMV", "去重活动GMV", "活动机制核销金额", "自然GMV", "活动机制活动ROI"]

        # 按指标顺序构建Excel数据
        for indicator in indicator_order:
            if indicator in marketing_data:
                indicator_data = marketing_data[indicator]

                # 格式化数值字段，添加千分位分隔符
                current_value = indicator_data.get("当期值", 0)
                base_value = indicator_data.get("对比期值", 0)
                change_value = indicator_data.get("变化值", 0)

                # 对GMV相关指标进行千分位格式化
                if 'GMV' in indicator or '核销金额' in indicator:
                    current_value_formatted = f"{current_value:,.0f}" if isinstance(current_value, (int, float)) else str(current_value)
                    base_value_formatted = f"{base_value:,.0f}" if isinstance(base_value, (int, float)) else str(base_value)
                    change_value_formatted = f"{change_value:,.0f}" if isinstance(change_value, (int, float)) else str(change_value)
                else:
                    # ROI等其他指标保持原有格式
                    current_value_formatted = f"{current_value:.4f}" if isinstance(current_value, (int, float)) else str(current_value)
                    base_value_formatted = f"{base_value:.4f}" if isinstance(base_value, (int, float)) else str(base_value)
                    change_value_formatted = f"{change_value:.4f}" if isinstance(change_value, (int, float)) else str(change_value)

                excel_row = {
                    "指标名称": indicator,
                    "当期GMV": current_value_formatted,  # 统一字段名称
                    "对比期GMV": base_value_formatted,   # 统一字段名称
                    "GMV变化值": change_value_formatted,  # 统一字段名称
                    "变化率": indicator_data.get("变化率", "0.00%"),
                    "贡献度": indicator_data.get("贡献度", "0.00%")
                }
                excel_data.append(excel_row)

        # 创建DataFrame
        marketing_df = pd.DataFrame(excel_data) if excel_data else pd.DataFrame()

        # 处理机制数据，创建机制详情的DataFrame
        mechanism_dfs = {}
        if "活动GMV" in marketing_data and "活动机制" in marketing_data["活动GMV"]:
            mechanisms_data = marketing_data["活动GMV"]["活动机制"]

            # 创建机制详情数据
            mechanism_excel_data = []
            for mechanism_name, mechanism_info in mechanisms_data.items():
                # 跳过内部数据字段
                if mechanism_name.startswith('_'):
                    continue

                mechanism_row = {
                    "机制名称": mechanism_name,
                    "机制类型": mechanism_info.get("机制类型", "未知"),
                    "当期GMV": mechanism_info.get("当期GMV", "0"),
                    "对比期GMV": mechanism_info.get("对比期GMV", "0"),
                    "GMV变化值": mechanism_info.get("GMV变化值", "0"),
                    "GMV变化率": mechanism_info.get("变化率", "0.00%"),
                    "GMV贡献度": mechanism_info.get("贡献度", "0.00%"),
                    "当期核销金额": mechanism_info.get("当期核销金额", "0"),
                    "对比期核销金额": mechanism_info.get("对比期核销金额", "0"),
                    "核销金额变化值": mechanism_info.get("核销金额变化值", "0"),
                    "核销金额变化率": mechanism_info.get("核销金额变化率", "0.00%"),
                    "核销金额贡献度": mechanism_info.get("核销金额贡献度", "0.00%"),
                    "当期ROI": mechanism_info.get("当期ROI", 0),
                    "对比期ROI": mechanism_info.get("对比期ROI", 0),
                    "ROI变化值": mechanism_info.get("ROI变化值", 0),
                    "ROI变化率": mechanism_info.get("ROI变化率", "0.00%"),
                    "ROI贡献度": mechanism_info.get("ROI贡献度", "0.00%")
                }
                mechanism_excel_data.append(mechanism_row)

            # 创建机制详情DataFrame
            if mechanism_excel_data:
                mechanism_df = pd.DataFrame(mechanism_excel_data)
                mechanism_dfs["促销项活动GMV机制详情"] = mechanism_df
                print(f"创建促销项活动GMV机制详情工作表，包含{len(mechanism_excel_data)}个机制")

            # 处理机制类型数据
            if "_mechanism_types" in mechanisms_data:
                mechanism_types_data = mechanisms_data["_mechanism_types"]
                mechanism_type_excel_data = []

                for type_name, type_info in mechanism_types_data.items():
                    type_row = {
                        "机制类型": type_name,
                        "当期GMV": type_info.get("当期GMV", "0"),
                        "对比期GMV": type_info.get("对比期GMV", "0"),
                        "GMV变化值": type_info.get("GMV变化值", "0"),
                        "GMV变化率": type_info.get("变化率", "0.00%"),
                        "GMV贡献度": type_info.get("贡献度", "0.00%"),
                        "当期核销金额": type_info.get("当期核销金额", "0"),
                        "对比期核销金额": type_info.get("对比期核销金额", "0"),
                        "核销金额变化值": type_info.get("核销金额变化值", "0"),
                        "核销金额变化率": type_info.get("核销金额变化率", "0.00%"),
                        "核销金额贡献度": type_info.get("核销金额贡献度", "0.00%"),
                        "当期ROI": type_info.get("当期ROI", 0),
                        "对比期ROI": type_info.get("对比期ROI", 0),
                        "ROI变化值": type_info.get("ROI变化值", 0),
                        "ROI变化率": type_info.get("ROI变化率", "0.00%"),
                        "ROI贡献度": type_info.get("ROI贡献度", "0.00%")
                    }
                    mechanism_type_excel_data.append(type_row)

                # 创建机制类型DataFrame
                if mechanism_type_excel_data:
                    mechanism_type_df = pd.DataFrame(mechanism_type_excel_data)
                    mechanism_dfs["促销项活动GMV机制类型"] = mechanism_type_df
                    print(f"创建促销项活动GMV机制类型工作表，包含{len(mechanism_type_excel_data)}个机制类型")

        return {
            "indicators_data": marketing_data,
            "formula_description": formula_description,
            "summary": {
                "indicators_count": len(marketing_data),
                "formula": "GMV = 活动机制核销金额 × 活动机制活动ROI" if attr_index == '活动GMV' else "GMV = 活动机制核销金额 × 活动机制活动ROI + 自然GMV"
            },
            "excel_dataframe": marketing_df,  # 促销项指标归因主表
            "mechanism_dataframes": mechanism_dfs  # 新增：机制详情和机制类型的DataFrame字典
        }

    except Exception as e:
        print(f"格式化促销项指标数据失败: {e}")
        import traceback
        traceback.print_exc()
        return {}

def get_marketing_activity_drill_down_data_for_excel(flag, brand, tar_date, base_date, tar_start_date, tar_end_date,
                                                    base_start_date, base_end_date, sub_brand, province, city,
                                                    retailer, platform, upc, coupon_mechanism, coupon_threshold,
                                                    coupon_discount, attr_index):
    """
    获取促销项活动GMV下钻数据用于Excel导出

    Args:
        flag: 时间类型标识(1:单日期, 2:日期范围)
        brand: 品牌
        tar_date: 目标日期
        base_date: 基准日期
        tar_start_date: 目标开始日期
        tar_end_date: 目标结束日期
        base_start_date: 基准开始日期
        base_end_date: 基准结束日期
        sub_brand: 子品牌筛选
        province: 省份筛选
        city: 城市筛选
        retailer: 零售商筛选
        platform: 平台筛选
        upc: 商品筛选
        coupon_mechanism: 券机制筛选
        coupon_threshold: 券门槛筛选
        coupon_discount: 优惠力度筛选
        attr_index: 归因指标类型

    Returns:
        dict: 包含各维度下钻数据的字典，键为工作表名，值为DataFrame
    """
    try:
        print("开始获取促销项活动GMV下钻数据用于Excel导出")

        # 只有当归因指标为活动GMV时才获取下钻数据
        if attr_index != '活动GMV':
            print("当前归因指标不是活动GMV，跳过下钻数据获取")
            return {}

        drill_down_sheets = {}

        # 定义活动GMV支持的下钻维度
        activity_drill_dimensions = ['券机制', '券门槛', '优惠力度', '平台', '省份', '城市', '零售商', '子品牌']

        for dimension in activity_drill_dimensions:
            try:
                print(f"获取{dimension}维度的活动GMV下钻数据")

                # 调用活动GMV下钻函数
                drill_data = get_drill_down_activity_result(
                    flag=flag,
                    brand=brand,
                    tar_date=tar_date,
                    base_date=base_date,
                    tar_start_date=tar_start_date,
                    tar_end_date=tar_end_date,
                    base_start_date=base_start_date,
                    base_end_date=base_end_date,
                    target_dimension=dimension,
                    sub_brand=sub_brand,
                    province=province,
                    city=city,
                    retailer=retailer,
                    platform=platform,
                    upc=upc,
                    coupon_mechanism=coupon_mechanism,
                    coupon_threshold=coupon_threshold,
                    coupon_discount=coupon_discount
                )

                if drill_data and len(drill_data) > 0:
                    # 将下钻数据转换为DataFrame
                    drill_df = pd.DataFrame(drill_data)

                    # 设置工作表名称
                    sheet_name = f"活动GMV下钻-{dimension}"
                    drill_down_sheets[sheet_name] = drill_df

                    print(f"成功获取{dimension}维度下钻数据，包含{len(drill_data)}条记录")
                else:
                    print(f"{dimension}维度下钻数据为空")

            except Exception as e:
                print(f"获取{dimension}维度下钻数据失败: {e}")
                continue

        print(f"促销项活动GMV下钻数据获取完成，共{len(drill_down_sheets)}个维度")
        return drill_down_sheets

    except Exception as e:
        print(f"获取促销项活动GMV下钻数据失败: {e}")
        import traceback
        traceback.print_exc()
        return {}

def calculate_lmdi_contribution_2_factors(Y0, Y1, A0, A1, B0, B1):
    """
    使用LMDI算法计算2个乘法因子的贡献度

    GMV(Y) = A × B

    Args:
        Y0, Y1: 对比期和当期的总GMV
        A0, A1: 对比期和当期的第一个因子
        B0, B1: 对比期和当期的第二个因子

    Returns:
        dict: 包含各参数贡献度的字典
    """
    import math

    try:
        print(f"LMDI 2因子计算开始: Y0={Y0}, Y1={Y1}, A0={A0}, A1={A1}, B0={B0}, B1={B1}")

        # 验证输入参数
        if Y0 <= 0 or Y1 <= 0 or A0 <= 0 or A1 <= 0 or B0 <= 0 or B1 <= 0:
            print(f"LMDI 2因子计算失败: 存在非正数参数")
            return {}

        # 验证分解关系
        expected_Y0 = A0 * B0
        expected_Y1 = A1 * B1
        if abs(Y0 - expected_Y0) > 1e-6 or abs(Y1 - expected_Y1) > 1e-6:
            print(f"警告：分解关系验证失败，Y0={Y0}, A0*B0={expected_Y0}, Y1={Y1}, A1*B1={expected_Y1}")

        # 平均对数权重
        ratio_Y = Y1/Y0
        if ratio_Y <= 0:
            print(f"LMDI 2因子计算失败: Y1/Y0={ratio_Y} <= 0")
            return {}

        L = (Y1 - Y0) / math.log(ratio_Y)
        Ca = L * math.log(A1/A0)
        Cb = L * math.log(B1/B0)

        print(f"LMDI中间计算: L={L}, Ca={Ca}, Cb={Cb}, Ca+Cb={Ca+Cb}, Y1-Y0={Y1-Y0}")

        # LMDI贡献度计算
        Ia = Ca / Y0
        Ib = Cb / Y0

        # 最终贡献度计算 - 修复除法问题
        growth_rate = (Y1 - Y0) / Y0
        if abs(growth_rate) < 1e-10:  # 避免除零
            print(f"LMDI 2因子计算失败: 增长率过小 {growth_rate}")
            return {}

        La = Ia / growth_rate  # = Ca/(Y1 - Y0)
        Lb = Ib / growth_rate

        print(f"LMDI贡献度计算: La={La}, Lb={Lb}, La+Lb={La+Lb}")

        # 根据大盘涨跌调整贡献度总和
        # 如果大盘是涨的，贡献度总和应该是1（100%）
        # 如果大盘是跌的，贡献度总和应该是-1（-100%）
        current_sum = La + Lb
        if abs(current_sum) < 1e-10:  # 避免除零错误
            print(f"LMDI 2因子计算失败: 贡献度总和为0")
            return {}

        if Y1 > Y0:  # 大盘上涨
            target_sum = 1.0
        else:  # 大盘下跌
            target_sum = -1.0

        # 按比例调整各因子贡献度
        adjustment_factor = target_sum / current_sum
        La_adjusted = La * adjustment_factor
        Lb_adjusted = Lb * adjustment_factor

        # 确保贡献度总和严格等于目标值（避免浮点数精度问题）
        actual_sum = La_adjusted + Lb_adjusted
        if abs(actual_sum - target_sum) > 1e-10:
            # 将误差分配给第一个因子
            La_adjusted = target_sum - Lb_adjusted

        print(f"LMDI调整后: La={La_adjusted}, Lb={Lb_adjusted}, 总和={La_adjusted+Lb_adjusted}")

        return {
            "因子A": {
                "绝对贡献度": Ia,
                "相对贡献度": La_adjusted
            },
            "因子B": {
                "绝对贡献度": Ib,
                "相对贡献度": Lb_adjusted
            }
        }
    except Exception as e:
        print(f"LMDI 2因子计算出错: {e}")
        import traceback
        traceback.print_exc()
        return {}

def group_mechanisms_by_type(mechanisms_data):
    try:
        mechanism_types = {}
        mechanism_type_mapping = {}

        # 按机制类型分组
        for mechanism_name, mechanism_data in mechanisms_data.items():
            mechanism_type = mechanism_data.get("机制类型", "其他机制")
            mechanism_type_mapping[mechanism_name] = mechanism_type

            if mechanism_type not in mechanism_types:
                mechanism_types[mechanism_type] = {
                    "mechanisms": [],
                    "target_gmv": 0,
                    "base_gmv": 0,
                    "target_subsidy": 0,
                    "base_subsidy": 0,
                    "target_roi_sum": 0,
                    "base_roi_sum": 0,
                    "mechanism_count": 0
                }

            # 添加机制到类型中
            mechanism_types[mechanism_type]["mechanisms"].append(mechanism_name)
            mechanism_types[mechanism_type]["mechanism_count"] += 1

            # 累加数值（需要解析格式化的字符串）
            try:
                target_gmv = float(mechanism_data.get("当期GMV", "0").replace(",", ""))
                base_gmv = float(mechanism_data.get("对比期GMV", "0").replace(",", ""))
                target_subsidy = float(mechanism_data.get("当期核销金额", "0").replace(",", ""))
                base_subsidy = float(mechanism_data.get("对比期核销金额", "0").replace(",", ""))
                target_roi = float(mechanism_data.get("当期ROI", 0))
                base_roi = float(mechanism_data.get("对比期ROI", 0))

                mechanism_types[mechanism_type]["target_gmv"] += target_gmv
                mechanism_types[mechanism_type]["base_gmv"] += base_gmv
                mechanism_types[mechanism_type]["target_subsidy"] += target_subsidy
                mechanism_types[mechanism_type]["base_subsidy"] += base_subsidy
                mechanism_types[mechanism_type]["target_roi_sum"] += target_roi
                mechanism_types[mechanism_type]["base_roi_sum"] += base_roi

            except (ValueError, TypeError) as e:
                print(f"解析机制数据时出错 {mechanism_name}: {e}")
                continue

        # 计算总的活动GMV变化值，用于计算贡献度
        total_gmv_change = sum(type_data["target_gmv"] - type_data["base_gmv"] for type_data in mechanism_types.values())

        # 使用LMDI算法计算机制类型贡献度
        mechanism_type_names = list(mechanism_types.keys())
        mechanism_type_count = len(mechanism_type_names)

        if mechanism_type_count > 1 and total_gmv_change != 0:
            print(f"使用LMDI算法计算{mechanism_type_count}个机制类型的贡献度")

            # 准备LMDI算法的数据
            base_values = []
            target_values = []

            for mechanism_type in mechanism_type_names:
                type_data = mechanism_types[mechanism_type]
                base_values.append(type_data["base_gmv"])
                target_values.append(type_data["target_gmv"])

            # 计算LMDI贡献度
            lmdi_contributions = calculate_lmdi_contribution_multiple_factors(
                sum(base_values), sum(target_values), base_values, target_values
            )

            if lmdi_contributions:
                print(f"LMDI算法计算成功，共{len(lmdi_contributions)}个因子")
                # 验证贡献度总和
                total_contribution = sum(contrib * 100 for contrib in lmdi_contributions)
                target_sum = 100.0 if total_gmv_change > 0 else -100.0
                print(f"机制类型贡献度总和: {total_contribution:.2f}%, 预期: {target_sum}%")

                # 如果总和不等于目标值，进行调整
                if abs(total_contribution - target_sum) > 0.01:
                    print(f"调整机制类型贡献度总和从{total_contribution:.2f}%到{target_sum}%")
                    adjustment_factor = target_sum / total_contribution
                    lmdi_contributions = [contrib * adjustment_factor for contrib in lmdi_contributions]
            else:
                print("LMDI算法失败，使用传统方法")
                lmdi_contributions = None
        else:
            lmdi_contributions = None

        # 计算每个机制类型的汇总指标
        for i, (mechanism_type, type_data) in enumerate(mechanism_types.items()):
            target_gmv = type_data["target_gmv"]
            base_gmv = type_data["base_gmv"]
            gmv_change = target_gmv - base_gmv
            gmv_change_rate = (gmv_change / base_gmv * 100) if base_gmv != 0 else 0

            # 计算贡献度
            if lmdi_contributions and i < len(lmdi_contributions):
                # 使用LMDI算法计算的贡献度
                contribution = lmdi_contributions[i] * 100
            else:
                # 使用传统方法：该机制类型的变化值 / 总变化值 * 100%（使用修正的贡献度计算规则）
                if total_gmv_change != 0:
                    # 计算原始贡献度比例
                    contribution_ratio = gmv_change / total_gmv_change

                    # 根据大盘涨跌决定贡献度符号
                    if total_gmv_change > 0:
                        # 大盘上涨，使用原始比例
                        contribution = contribution_ratio * 100
                    else:
                        # 大盘下跌，使用绝对值比例并加负号
                        contribution = -abs(contribution_ratio) * 100
                else:
                    contribution = 0

            # 计算平均ROI
            mechanism_count = type_data["mechanism_count"]
            avg_target_roi = type_data["target_roi_sum"] / mechanism_count if mechanism_count > 0 else 0
            avg_base_roi = type_data["base_roi_sum"] / mechanism_count if mechanism_count > 0 else 0

            # 更新为最终格式
            mechanism_types[mechanism_type] = {
                "当期值": f"{round(target_gmv, 2):,.0f}",
                "对比期值": f"{round(base_gmv, 2):,.0f}",
                "变化值": f"{round(gmv_change, 2):,.0f}",
                "变化率": f"{gmv_change_rate:.2f}%",
                "贡献度": f"{contribution:.2f}%",
                "当期ROI": round(avg_target_roi, 4),
                "对比期ROI": round(avg_base_roi, 4),
                "mechanisms": type_data["mechanisms"]
            }

        print(f"机制类型分组完成，共 {len(mechanism_types)} 个类型")
        for type_name, type_info in mechanism_types.items():
            print(f"  {type_name}: {len(type_info['mechanisms'])} 个机制")

        return {
            "mechanism_types": mechanism_types,
            "mechanism_type_mapping": mechanism_type_mapping
        }

    except Exception as e:
        print(f"机制类型分组时出错: {e}")
        import traceback
        traceback.print_exc()
        return {
            "mechanism_types": {},
            "mechanism_type_mapping": {}
        }

def calculate_lmdi_contribution_multiple_factors(Y0, Y1, base_values, target_values):
    """
    使用LMDI算法计算多个因子的贡献度（用于机制类型分解）

    参数:
    Y0: 基期总值
    Y1: 目标期总值
    base_values: 基期各因子值列表
    target_values: 目标期各因子值列表

    返回:
    各因子的相对贡献度列表（相对贡献度总和为1.0或-1.0）
    """
    try:
        if len(base_values) != len(target_values):
            print("基期和目标期因子数量不匹配")
            return None

        if Y0 <= 0 or Y1 <= 0:
            print(f"总值必须为正数: Y0={Y0}, Y1={Y1}")
            return None

        if any(val <= 0 for val in base_values + target_values):
            print("所有因子值必须为正数")
            return None

        n = len(base_values)
        if n < 2:
            print("至少需要2个因子")
            return None

        # 计算总变化
        delta_Y = Y1 - Y0

        if abs(delta_Y) < 1e-10:
            print("总变化值过小，无法计算贡献度")
            return None

        # 计算各因子的LMDI贡献度
        contributions = []

        for i in range(n):
            X0_i = base_values[i]
            X1_i = target_values[i]

            if X0_i <= 0 or X1_i <= 0:
                contributions.append(0.0)
                continue

            # 计算该因子的对数平均权重
            if abs(X1_i - X0_i) < 1e-10:
                # 如果因子没有变化，贡献度为0
                contributions.append(0.0)
            else:
                # LMDI公式：L(X1_i, X0_i) * ln(X1_i / X0_i)
                L_weight = (X1_i - X0_i) / math.log(X1_i / X0_i)
                contribution = L_weight * math.log(X1_i / X0_i)
                contributions.append(contribution)

        # 计算相对贡献度
        total_contribution = sum(contributions)

        if abs(total_contribution) < 1e-10:
            print("总贡献度为0，无法计算相对贡献度")
            return None

        # 相对贡献度 = 各因子贡献度 / 总贡献度
        relative_contributions = [contrib / total_contribution for contrib in contributions]

        # 根据大盘涨跌调整贡献度总和
        current_sum = sum(relative_contributions)
        if current_sum != 0:
            if Y1 > Y0:  # 大盘上涨
                target_sum = 1.0
            else:  # 大盘下跌
                target_sum = -1.0

            # 按比例调整各因子贡献度
            adjustment_factor = target_sum / current_sum
            relative_contributions = [contrib * adjustment_factor for contrib in relative_contributions]

            # 确保贡献度总和严格等于目标值（避免浮点数精度问题）
            actual_sum = sum(relative_contributions)
            if abs(actual_sum - target_sum) > 1e-10:
                # 将误差分配给第一个因子
                relative_contributions[0] = target_sum - sum(relative_contributions[1:])

        print(f"LMDI多因子计算完成: {n}个因子，总和={sum(relative_contributions):.6f}")
        return relative_contributions

    except Exception as e:
        print(f"LMDI多因子计算失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def calculate_lmdi_contribution_3_factors(Y0, Y1, A0, A1, B0, B1, C0, C1):
    """
    使用LMDI算法计算3个因子的贡献度（用于促销项指标）

    GMV(Y) = A × B + C （其中 A×B 是去重活动GMV，C 是自然GMV = 总GMV - 去重活动GMV）

    注意：这是一个加法分解，需要特殊处理

    Args:
        Y0, Y1: 对比期和当期的总GMV
        A0, A1: 对比期和当期的活动机制核销金额
        B0, B1: 对比期和当期的活动机制活动ROI
        C0, C1: 对比期和当期的自然GMV

    Returns:
        dict: 包含各参数贡献度的字典
    """
    try:
        # 对于加法分解 Y = A×B + C，我们分别计算各部分的贡献
        # 活动GMV部分的变化
        activity_gmv_0 = A0 * B0
        activity_gmv_1 = A1 * B1
        activity_gmv_change = activity_gmv_1 - activity_gmv_0

        # 自然GMV部分的变化
        natural_gmv_change = C1 - C0

        # 总GMV变化
        total_gmv_change = Y1 - Y0

        # 验证分解关系
        expected_change = activity_gmv_change + natural_gmv_change
        if abs(expected_change - total_gmv_change) > 1e-6:
            print(f"警告：分解关系验证失败，预期变化={expected_change}，实际变化={total_gmv_change}")

        # 对活动GMV部分使用2因子LMDI分解
        if activity_gmv_0 > 0 and activity_gmv_1 > 0 and A0 > 0 and A1 > 0 and B0 > 0 and B1 > 0:
            activity_lmdi = calculate_lmdi_contribution_2_factors(activity_gmv_0, activity_gmv_1, A0, A1, B0, B1)

            if activity_lmdi and total_gmv_change != 0:
                # 将活动GMV内部的贡献度转换为对总GMV的贡献度
                subsidy_contribution = activity_lmdi['因子A']['相对贡献度'] * (activity_gmv_change / total_gmv_change)
                roi_contribution = activity_lmdi['因子B']['相对贡献度'] * (activity_gmv_change / total_gmv_change)
                natural_contribution = natural_gmv_change / total_gmv_change

                # 根据大盘涨跌调整贡献度总和
                current_sum = subsidy_contribution + roi_contribution + natural_contribution
                print(f"LMDI 3因子调整前: 核销金额={subsidy_contribution:.4f}, ROI={roi_contribution:.4f}, 自然GMV={natural_contribution:.4f}, 总和={current_sum:.4f}")

                if current_sum != 0:  # 避免除零错误
                    if Y1 > Y0:  # 大盘上涨
                        target_sum = 1.0
                    else:  # 大盘下跌
                        target_sum = -1.0

                    # 按比例调整各因子贡献度
                    adjustment_factor = target_sum / current_sum
                    subsidy_contribution = subsidy_contribution * adjustment_factor
                    roi_contribution = roi_contribution * adjustment_factor
                    natural_contribution = natural_contribution * adjustment_factor

                    print(f"LMDI 3因子调整后: 核销金额={subsidy_contribution:.4f}, ROI={roi_contribution:.4f}, 自然GMV={natural_contribution:.4f}, 总和={subsidy_contribution + roi_contribution + natural_contribution:.4f}")
                else:
                    print("警告：LMDI 3因子贡献度总和为0，无法调整")

                return {
                    "活动机制核销金额": {
                        "绝对贡献度": activity_gmv_change / Y0 * activity_lmdi['因子A']['相对贡献度'],
                        "相对贡献度": subsidy_contribution
                    },
                    "活动机制活动ROI": {
                        "绝对贡献度": activity_gmv_change / Y0 * activity_lmdi['因子B']['相对贡献度'],
                        "相对贡献度": roi_contribution
                    },
                    "自然GMV": {
                        "绝对贡献度": natural_gmv_change / Y0,
                        "相对贡献度": natural_contribution
                    }
                }

        # 特殊情况：活动GMV为0或接近0时，所有贡献度归给自然GMV
        elif activity_gmv_0 == 0 and activity_gmv_1 == 0 and total_gmv_change != 0:
            print("活动GMV为0，所有贡献度归给自然GMV")
            # 根据大盘涨跌确定贡献度符号
            natural_contribution = 1.0 if Y1 > Y0 else -1.0
            return {
                "活动机制核销金额": {
                    "绝对贡献度": 0.0,
                    "相对贡献度": 0.0
                },
                "活动机制活动ROI": {
                    "绝对贡献度": 0.0,
                    "相对贡献度": 0.0
                },
                "自然GMV": {
                    "绝对贡献度": natural_gmv_change / Y0,
                    "相对贡献度": natural_contribution
                }
            }

        # 回退到传统方法
        if total_gmv_change != 0:
            # 如果活动GMV变化为0，所有贡献度归给自然GMV
            if activity_gmv_change == 0:
                # 根据大盘涨跌确定贡献度符号
                natural_contribution = 1.0 if Y1 > Y0 else -1.0
                return {
                    "活动机制核销金额": {
                        "绝对贡献度": 0.0,
                        "相对贡献度": 0.0
                    },
                    "活动机制活动ROI": {
                        "绝对贡献度": 0.0,
                        "相对贡献度": 0.0
                    },
                    "自然GMV": {
                        "绝对贡献度": natural_gmv_change / Y0,
                        "相对贡献度": natural_contribution
                    }
                }
            else:
                # 有活动GMV变化时，使用简化分配
                subsidy_contribution = activity_gmv_change / total_gmv_change * 0.5
                roi_contribution = activity_gmv_change / total_gmv_change * 0.5
                natural_contribution = natural_gmv_change / total_gmv_change

                # 根据大盘涨跌调整贡献度总和
                current_sum = subsidy_contribution + roi_contribution + natural_contribution
                print(f"传统方法调整前: 核销金额={subsidy_contribution:.4f}, ROI={roi_contribution:.4f}, 自然GMV={natural_contribution:.4f}, 总和={current_sum:.4f}")

                if current_sum != 0:  # 避免除零错误
                    if Y1 > Y0:  # 大盘上涨
                        target_sum = 1.0
                    else:  # 大盘下跌
                        target_sum = -1.0

                    # 按比例调整各因子贡献度
                    adjustment_factor = target_sum / current_sum
                    subsidy_contribution = subsidy_contribution * adjustment_factor
                    roi_contribution = roi_contribution * adjustment_factor
                    natural_contribution = natural_contribution * adjustment_factor

                    print(f"传统方法调整后: 核销金额={subsidy_contribution:.4f}, ROI={roi_contribution:.4f}, 自然GMV={natural_contribution:.4f}, 总和={subsidy_contribution + roi_contribution + natural_contribution:.4f}")
                else:
                    print("警告：传统方法贡献度总和为0，无法调整")

                return {
                    "活动机制核销金额": {
                        "绝对贡献度": activity_gmv_change / Y0 * 0.5,  # 简化分配
                        "相对贡献度": subsidy_contribution
                    },
                    "活动机制活动ROI": {
                        "绝对贡献度": activity_gmv_change / Y0 * 0.5,  # 简化分配
                        "相对贡献度": roi_contribution
                    },
                    "自然GMV": {
                        "绝对贡献度": natural_gmv_change / Y0,
                        "相对贡献度": natural_contribution
                    }
                }
        else:
            return {}

    except Exception as e:
        print(f"LMDI 3因子计算出错: {e}")
        return {}

def calculate_lmdi_contribution_4_factors(Y0, Y1, A0, A1, B0, B1, C0, C1, D0, D1):
    """
    使用LMDI算法计算4个乘法因子的贡献度

    GMV(Y) = A × B × C × D

    Args:
        Y0, Y1: 对比期和当期的总GMV
        A0, A1: 对比期和当期的第一个因子
        B0, B1: 对比期和当期的第二个因子
        C0, C1: 对比期和当期的第三个因子
        D0, D1: 对比期和当期的第四个因子

    Returns:
        dict: 包含各参数贡献度的字典
    """
    import math

    try:
        # 平均对数权重
        L = (Y1 - Y0) / (math.log(Y1/Y0))
        Ca = L * math.log(A1/A0)
        Cb = L * math.log(B1/B0)
        Cc = L * math.log(C1/C0)
        Cd = L * math.log(D1/D0)

        # LMDI贡献度计算
        Ia = Ca / Y0
        Ib = Cb / Y0
        Ic = Cc / Y0
        Id = Cd / Y0

        # 最终贡献度计算
        La = Ia / ((Y1 - Y0) / Y0)  # = Ca/(Y1 - Y0)
        Lb = Ib / ((Y1 - Y0) / Y0)
        Lc = Ic / ((Y1 - Y0) / Y0)
        Ld = Id / ((Y1 - Y0) / Y0)

        # 根据大盘涨跌调整贡献度总和
        # 如果大盘是涨的，贡献度总和应该是1（100%）
        # 如果大盘是跌的，贡献度总和应该是-1（-100%）
        current_sum = La + Lb + Lc + Ld
        if current_sum != 0:  # 避免除零错误
            if Y1 > Y0:  # 大盘上涨
                target_sum = 1.0
            else:  # 大盘下跌
                target_sum = -1.0

            # 按比例调整各因子贡献度
            adjustment_factor = target_sum / current_sum
            La = La * adjustment_factor
            Lb = Lb * adjustment_factor
            Lc = Lc * adjustment_factor
            Ld = Ld * adjustment_factor

        return {
            "因子A": {
                "绝对贡献度": Ia,
                "相对贡献度": La
            },
            "因子B": {
                "绝对贡献度": Ib,
                "相对贡献度": Lb
            },
            "因子C": {
                "绝对贡献度": Ic,
                "相对贡献度": Lc
            },
            "因子D": {
                "绝对贡献度": Id,
                "相对贡献度": Ld
            }
        }
    except Exception as e:
        print(f"LMDI 4因子计算出错: {e}")
        return {}

def calculate_lmdi_contribution(Y0, Y1, A0, A1, B0, B1, C0, C1, D0, D1, E0, E1):
    """
    使用LMDI算法计算乘法因子贡献度

    GMV(Y) = 覆盖门店数 x 门店动销转化率 x 动销单店GMV
           = 覆盖门店数 x 门店动销转化率 x 在线sku宽度 x 在线sku动销转化率 x 动销单sku GMV
           = A x B x C x D x E

    Args:
        Y0, Y1: 对比期和当期的总GMV
        A0, A1: 对比期和当期的覆盖门店数
        B0, B1: 对比期和当期的门店动销转化率
        C0, C1: 对比期和当期的在线sku宽度
        D0, D1: 对比期和当期的在线sku动销转化率
        E0, E1: 对比期和当期的动销单sku GMV

    Returns:
        dict: 包含各参数贡献度的字典
    """
    import math

    try:
        # 平均对数权重
        L = (Y1 - Y0) / (math.log(Y1/Y0))
        Ca = L * math.log(A1/A0)
        Cb = L * math.log(B1/B0)
        Cc = L * math.log(C1/C0)
        Cd = L * math.log(D1/D0)
        Ce = L * math.log(E1/E0)

        # LMDI贡献度计算
        Ia = Ca / Y0
        Ib = Cb / Y0
        Ic = Cc / Y0
        Id = Cd / Y0
        Ie = Ce / Y0

        # 最终贡献度计算
        La = Ia / ((Y1 - Y0) / Y0)  # = (Ca / Y0)/((Y1 - Y0) / Y0) = Ca/(Y1 - Y0)
        Lb = Ib / ((Y1 - Y0) / Y0)
        Lc = Ic / ((Y1 - Y0) / Y0)
        Ld = Id / ((Y1 - Y0) / Y0)
        Le = Ie / ((Y1 - Y0) / Y0)

        # 根据大盘涨跌调整贡献度总和
        # 如果大盘是涨的，贡献度总和应该是1（100%）
        # 如果大盘是跌的，贡献度总和应该是-1（-100%）
        current_sum = La + Lb + Lc + Ld + Le
        if current_sum != 0:  # 避免除零错误
            if Y1 > Y0:  # 大盘上涨
                target_sum = 1.0
            else:  # 大盘下跌
                target_sum = -1.0

            # 按比例调整各因子贡献度
            adjustment_factor = target_sum / current_sum
            La = La * adjustment_factor
            Lb = Lb * adjustment_factor
            Lc = Lc * adjustment_factor
            Ld = Ld * adjustment_factor
            Le = Le * adjustment_factor

        return {
            "覆盖门店数": {
                "绝对贡献度": Ia,
                "相对贡献度": La
            },
            "门店动销转化率": {
                "绝对贡献度": Ib,
                "相对贡献度": Lb
            },
            "在线sku宽度": {
                "绝对贡献度": Ic,
                "相对贡献度": Lc
            },
            "在线sku动销转化率": {
                "绝对贡献度": Id,
                "相对贡献度": Ld
            },
            "动销单sku GMV": {
                "绝对贡献度": Ie,
                "相对贡献度": Le
            }
        }
    except Exception as e:
        print(f"LMDI计算出错: {e}")
        return {}
def format_lmdi_result_for_display(result, Y0, Y1):
    """
    格式化LMDI结果用于显示

    Args:
        result: calculate_lmdi_contribution的返回结果
        Y0, Y1: 对比期和当期GMV

    Returns:
        dict: 格式化后的结果
    """
    formatted_result = {}

    for param, values in result.items():
        abs_contrib = values['绝对贡献度']
        rel_contrib = values['相对贡献度']

        formatted_result[param] = {
            "绝对贡献度": f"{abs_contrib:.6f}",
            "相对贡献度": f"{rel_contrib:.6f}",
            "绝对贡献度百分比": f"{abs_contrib*100:.4f}%",
            "相对贡献度百分比": f"{rel_contrib*100:.4f}%"
        }

    # 添加汇总信息
    formatted_result["汇总信息"] = {
        "对比期GMV": f"{Y0:,.2f}",
        "当期GMV": f"{Y1:,.2f}",
        "GMV变化值": f"{Y1-Y0:,.2f}",
        "GMV变化率": f"{(Y1-Y0)/Y0*100:.4f}%"
    }

    return formatted_result
