# 促销项GMV归因分析需求文档

## 介绍

本功能旨在为促销团队提供精确的GMV归因分析工具，专门计算自然GMV和去重活动GMV的贡献度。系统将采用变化值/总变化值的计算方案，确保两个指标的贡献度之和始终为100%或-100%，为促销决策提供准确的数据支持。

## 需求

### 需求 1

**用户故事：** 作为促销分析师，我希望能够计算自然GMV和去重活动GMV的贡献度，以便准确评估不同渠道对总GMV变化的影响。

#### 验收标准

1. WHEN 用户输入自然GMV和去重活动GMV的数据 THEN 系统 SHALL 计算各自的变化值
2. WHEN 系统计算贡献度 THEN 系统 SHALL 使用变化值/总变化值的公式
3. WHEN 计算完成 THEN 自然GMV贡献度 + 去重活动GMV贡献度 SHALL 等于100%或-100%
4. IF 总变化值为0 THEN 系统 SHALL 显示无法计算贡献度的提示信息

### 需求 2

**用户故事：** 作为促销经理，我希望能够选择不同的时间周期进行对比分析，以便了解不同时期的GMV归因变化趋势。

#### 验收标准

1. WHEN 用户选择时间周期 THEN 系统 SHALL 支持日、周、月、季度、年度等时间维度
2. WHEN 用户选择对比周期 THEN 系统 SHALL 自动获取对应时间段的GMV数据
3. WHEN 时间周期变更 THEN 系统 SHALL 重新计算贡献度并更新显示结果
4. IF 选择的时间周期数据不完整 THEN 系统 SHALL 提示数据缺失并建议替代方案

### 需求 3

**用户故事：** 作为数据分析师，我希望系统能够展示详细的计算过程和中间结果，以便验证计算逻辑的正确性。

#### 验收标准

1. WHEN 计算贡献度 THEN 系统 SHALL 显示自然GMV的基期值、当期值和变化值
2. WHEN 计算贡献度 THEN 系统 SHALL 显示去重活动GMV的基期值、当期值和变化值
3. WHEN 显示结果 THEN 系统 SHALL 展示总变化值的计算过程
4. WHEN 显示贡献度 THEN 系统 SHALL 同时显示百分比和绝对数值
5. IF 用户点击详情 THEN 系统 SHALL 展开显示完整的计算公式和步骤

### 需求 4

**用户故事：** 作为营销团队成员，我希望能够导出分析结果，以便在报告和会议中使用这些数据。

#### 验收标准

1. WHEN 用户点击导出 THEN 系统 SHALL 支持Excel格式导出
2. WHEN 导出数据 THEN 系统 SHALL 包含原始数据、计算过程和最终结果
3. WHEN 导出完成 THEN 系统 SHALL 生成包含时间戳的文件名
4. IF 导出失败 THEN 系统 SHALL 显示错误信息并提供重试选项

### 需求 5

**用户故事：** 作为系统管理员，我希望系统能够处理异常情况和边界条件，确保分析结果的准确性和系统的稳定性。

#### 验收标准

1. WHEN 输入数据包含负值 THEN 系统 SHALL 正确处理并计算贡献度
2. WHEN 总变化值接近0 THEN 系统 SHALL 提供高精度计算或提示计算不稳定
3. WHEN 数据源连接失败 THEN 系统 SHALL 显示友好的错误提示
4. WHEN 计算过程中出现异常 THEN 系统 SHALL 记录错误日志并提供恢复建议
5. IF 用户输入无效数据格式 THEN 系统 SHALL 进行数据验证并提示正确格式